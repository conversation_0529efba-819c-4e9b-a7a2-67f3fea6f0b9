# 📋 API Documentation - GST Breakdown Integration

## 🔄 Changes Summary
- Added separate GST component fields (`cgst_amount`, `sgst_amount`, `igst_amount`, `ugst_amount`)
- Added `service_charge` field
- Added structured `tax_breakdown` array
- Enhanced both Cart and Order APIs

**Base URL:** `http://localhost:8000`

---

## 🛒 CART APIs - Updated with GST Breakdown

### 1. Get Cart Details
```http
GET http://localhost:8000/api/cart/
```

**Response (Updated):**
```json
{
  "id": 195,
  "user": null,
  "session_key": "9yzjifhcdve2n1cvpsh7qjg6axi19r5v",
  "created_at": "2025-06-16T15:42:44.747021+05:30",
  "updated_at": "2025-06-16T15:42:45.375310+05:30",
  "is_active": true,
  "sub_total": "780.00",
  "tax_amount": "140.40",
  "discount_amount": "0.00",
  "minimum_order_fee_applied": "0.00",
  "total_amount": "939.90",
  "coupon_code_applied": null,
  
  // 🆕 NEW GST BREAKDOWN FIELDS
  "cgst_amount": "70.20",
  "sgst_amount": "70.20", 
  "igst_amount": "0.00",
  "ugst_amount": "0.00",
  "service_charge": "19.50",
  "tax_breakdown": [
    {
      "type": "CGST",
      "rate": "9.00%",
      "amount": "70.20"
    },
    {
      "type": "SGST",
      "rate": "9.00%", 
      "amount": "70.20"
    },
    {
      "type": "Service Charge",
      "rate": "2.50%",
      "amount": "19.50"
    }
  ],
  
  "items": [...],
  "items_count": 1,
  "unique_services_count": 1
}
```

**Empty Cart Response:**
```json
{
  "id": null,
  "user": null,
  "session_key": null,
  "created_at": null,
  "updated_at": null,
  "is_active": true,
  "sub_total": "0.00",
  "tax_amount": "0.00",
  "discount_amount": "0.00",
  "minimum_order_fee_applied": "0.00",
  "total_amount": "0.00",
  "coupon_code_applied": null,
  
  // 🆕 NEW GST BREAKDOWN FIELDS (Empty)
  "cgst_amount": "0.00",
  "sgst_amount": "0.00",
  "igst_amount": "0.00", 
  "ugst_amount": "0.00",
  "service_charge": "0.00",
  "tax_breakdown": [],
  
  "items": [],
  "items_count": 0,
  "unique_services_count": 0
}
```

### 2. Add to Cart
```http
POST http://localhost:8000/api/cart/add/
Content-Type: application/json

{
  "service_id": 1,
  "quantity": 1
}
```

**Response:**
```json
{
  "success": true,
  "message": "Added 1 Basic Bathroom Cleaning Extra Large Size to cart",
  "cart": {
    // Same structure as Get Cart Details above with GST breakdown
    "sub_total": "780.00",
    "cgst_amount": "70.20",
    "sgst_amount": "70.20",
    "igst_amount": "0.00",
    "service_charge": "19.50",
    "tax_breakdown": [...],
    "total_amount": "939.90"
  }
}
```

### 3. Update Cart Item
```http
PUT http://localhost:8000/api/cart/items/{item_id}/
Content-Type: application/json

{
  "quantity": 2
}
```

### 4. Remove Cart Item
```http
DELETE http://localhost:8000/api/cart/items/{item_id}/
```

### 5. Clear Cart
```http
DELETE http://localhost:8000/api/cart/clear/
```

### 6. Apply Coupon
```http
POST http://localhost:8000/api/cart/apply-coupon/
Content-Type: application/json

{
  "coupon_code": "SAVE10"
}
```

---

## 📦 ORDER APIs - Updated with GST Breakdown

### 1. Get Order Details
```http
GET http://localhost:8000/api/orders/{order_id}/
```

**Response (Updated):**
```json
{
  "id": 123,
  "order_number": "ORD-2025-001",
  "customer": 45,
  "customer_name": "John Doe",
  "customer_mobile": "+919876543210",
  "status": "confirmed",
  "payment_status": "paid",
  "payment_method": "razorpay",
  "subtotal": "780.00",
  "tax_amount": "140.40",
  "discount_amount": "0.00",
  "total_amount": "939.90",
  
  // 🆕 NEW GST BREAKDOWN FIELDS
  "cgst_amount": "70.20",
  "sgst_amount": "70.20",
  "igst_amount": "0.00", 
  "ugst_amount": "0.00",
  "service_charge": "19.50",
  "tax_breakdown": [
    {
      "type": "CGST",
      "rate": "9%",
      "amount": "70.20"
    },
    {
      "type": "SGST",
      "rate": "9%",
      "amount": "70.20"
    },
    {
      "type": "Service Charge",
      "rate": "2.5%",
      "amount": "19.50"
    }
  ],
  
  "delivery_address": {...},
  "items": [...],
  "created_at": "2025-06-16T15:42:44.747021+05:30"
}
```

### 2. Create Order from Cart
```http
POST http://localhost:8000/api/orders/create/
Content-Type: application/json

{
  "cart_id": "195",
  "delivery_address": {
    "house_number": "123",
    "street_name": "Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001"
  },
  "payment_method": "razorpay",
  "scheduled_date": "2025-06-20",
  "customer_notes": "Please call before arriving"
}
```

### 3. List User Orders
```http
GET http://localhost:8000/api/orders/
```

---

## 🏪 CATALOGUE APIs

### 1. Get Services
```http
GET http://localhost:8000/api/catalogue/services/
```

### 2. Get Service Details
```http
GET http://localhost:8000/api/catalogue/services/{service_id}/
```

### 3. Get Categories
```http
GET http://localhost:8000/api/catalogue/categories/
```

---

## 🔐 AUTHENTICATION APIs

### 1. Send OTP
```http
POST http://localhost:8000/api/auth/send-otp/
Content-Type: application/json

{
  "mobile_number": "+919876543210"
}
```

### 2. Verify OTP & Login
```http
POST http://localhost:8000/api/auth/verify-otp/
Content-Type: application/json

{
  "mobile_number": "+919876543210",
  "otp": "123456"
}
```

---

## 🧪 Quick Test Commands

### Test Cart API:
```bash
# Get empty cart
curl http://localhost:8000/api/cart/

# Add item to cart
curl -X POST http://localhost:8000/api/cart/add/ \
  -H "Content-Type: application/json" \
  -d '{"service_id": 1, "quantity": 1}'

# Get cart with GST breakdown
curl http://localhost:8000/api/cart/
```

### Test Services API:
```bash
# Get all services
curl http://localhost:8000/api/catalogue/services/

# Get specific service
curl http://localhost:8000/api/catalogue/services/1/
```
