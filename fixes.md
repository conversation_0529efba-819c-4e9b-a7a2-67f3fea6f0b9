# 🛠️ Next.js Frontend Fixes for COD Orders

## 📁 **Files to Update in Your Next.js Project**

Copy these files to your Next.js project at:
`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer`

## 🔧 **Backend Fix Applied**

✅ **Fixed Django Backend**: `customer_notes` field now allows blank values
```python
# orders/serializers.py - Line 231
customer_notes = serializers.CharField(max_length=1000, required=False, allow_blank=True)
```

## 📝 **Frontend Files to Create/Update**

### **1. utils/orderApi.js** (Create this file)

```javascript
// utils/orderApi.js
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

// Get auth token from localStorage
const getAuthToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token');
  }
  return null;
};

// Create COD Order
export const createCODOrder = async (orderData) => {
  try {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/api/orders/cod/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        cart_id: orderData.cart_id,
        delivery_address: orderData.delivery_address,
        customer_notes: orderData.customer_notes || '', // Ensure it's never undefined
        scheduled_date: orderData.scheduled_date,
        scheduled_time_slot: orderData.scheduled_time_slot,
      }),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return {
        success: true,
        order: result.order,
        payment: result.payment,
        message: result.message,
      };
    } else {
      throw new Error(result.message || 'COD order creation failed');
    }
  } catch (error) {
    console.error('COD Order Error:', error);
    throw error;
  }
};

// Create Regular Order (for Razorpay)
export const createOrder = async (orderData) => {
  try {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/api/orders/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        cart_id: orderData.cart_id,
        delivery_address: orderData.delivery_address,
        payment_method: orderData.payment_method,
        customer_notes: orderData.customer_notes || '', // Ensure it's never undefined
        scheduled_date: orderData.scheduled_date,
        scheduled_time_slot: orderData.scheduled_time_slot,
      }),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return result;
    } else {
      throw new Error(result.message || 'Order creation failed');
    }
  } catch (error) {
    console.error('Order Creation Error:', error);
    throw error;
  }
};

// Initiate Payment (for Razorpay)
export const initiatePayment = async (paymentData) => {
  try {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/api/payments/initiate/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return result;
    } else {
      throw new Error(result.message || 'Payment initiation failed');
    }
  } catch (error) {
    console.error('Payment Initiation Error:', error);
    throw error;
  }
};

// Get Cart
export const getCart = async () => {
  try {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/api/cart/`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Get Cart Error:', error);
    throw error;
  }
};

// Get Payment Configuration
export const getPaymentConfiguration = async () => {
  try {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/api/payments/configuration/`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Get Payment Config Error:', error);
    throw error;
  }
};
```

### **2. components/Checkout.js** (Update this file)

```javascript
// components/Checkout.js
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { createCODOrder, createOrder, initiatePayment, getCart, getPaymentConfiguration } from '../utils/orderApi';

const Checkout = () => {
  const router = useRouter();
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('razorpay');
  const [paymentConfig, setPaymentConfig] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState({
    house_number: '',
    street_name: '',
    city: '',
    state: '',
    pincode: '',
  });
  const [customerNotes, setCustomerNotes] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');
  const [timeSlot, setTimeSlot] = useState('');

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load cart and payment configuration
      const [cartData, configData] = await Promise.all([
        getCart(),
        getPaymentConfiguration(),
      ]);
      
      setCart(cartData);
      setPaymentConfig(configData);
    } catch (error) {
      console.error('Error loading data:', error);
      alert('Error loading checkout data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    if (!cart || !cart.id) {
      alert('No items in cart');
      return false;
    }

    if (!deliveryAddress.house_number.trim()) {
      alert('House number is required');
      return false;
    }

    if (!deliveryAddress.street_name.trim()) {
      alert('Street name is required');
      return false;
    }

    if (!deliveryAddress.city.trim()) {
      alert('City is required');
      return false;
    }

    if (!deliveryAddress.state.trim()) {
      alert('State is required');
      return false;
    }

    if (!deliveryAddress.pincode.trim()) {
      alert('Pincode is required');
      return false;
    }

    return true;
  };

  const handleCODOrder = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const orderData = {
        cart_id: cart.id.toString(),
        delivery_address: deliveryAddress,
        customer_notes: customerNotes.trim(), // Ensure it's trimmed
        scheduled_date: scheduledDate || null,
        scheduled_time_slot: timeSlot || null,
      };

      const result = await createCODOrder(orderData);

      // Show success message with COD details
      const message = `
        COD Order Placed Successfully!
        Order Number: ${result.order.order_number}
        Original Amount: ₹${result.payment.original_amount}
        COD Charges: ₹${result.payment.cod_charges}
        Total Amount: ₹${result.payment.total_amount}
      `;
      
      alert(message);

      // Redirect to order confirmation
      router.push(`/orders/${result.order.order_number}`);
    } catch (error) {
      console.error('COD Order Error:', error);
      alert('COD Order Failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRazorpayOrder = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      // Step 1: Create order
      const orderData = {
        cart_id: cart.id.toString(),
        delivery_address: deliveryAddress,
        payment_method: 'razorpay',
        customer_notes: customerNotes.trim(),
        scheduled_date: scheduledDate || null,
        scheduled_time_slot: timeSlot || null,
      };

      const orderResult = await createOrder(orderData);

      // Step 2: Initiate payment
      const paymentData = {
        order_id: orderResult.order.id,
        amount: orderResult.order.total_amount,
        payment_method: 'razorpay',
        currency: 'INR',
      };

      const paymentResult = await initiatePayment(paymentData);

      // Step 3: Open Razorpay checkout
      const options = {
        key: paymentResult.razorpay_key_id,
        amount: paymentResult.amount * 100, // Amount in paise
        currency: paymentResult.currency,
        name: 'Home Services',
        description: `Order #${orderResult.order.order_number}`,
        order_id: paymentResult.razorpay_order_id,
        handler: function (response) {
          // Handle successful payment
          alert('Payment successful!');
          router.push(`/orders/${orderResult.order.order_number}`);
        },
        prefill: {
          name: 'Customer Name',
          email: '<EMAIL>',
          contact: '9999999999',
        },
        theme: {
          color: '#3399cc',
        },
      };

      const rzp = new window.Razorpay(options);
      rzp.open();
    } catch (error) {
      console.error('Razorpay Order Error:', error);
      alert('Order Failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePlaceOrder = () => {
    if (paymentMethod === 'cod') {
      handleCODOrder();
    } else {
      handleRazorpayOrder();
    }
  };

  if (loading && !cart) {
    return <div className="loading">Loading checkout...</div>;
  }

  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="empty-cart">
        <h2>Your cart is empty</h2>
        <p>Please add items to your cart before checkout.</p>
        <button onClick={() => router.push('/services')}>
          Browse Services
        </button>
      </div>
    );
  }

  return (
    <div className="checkout-container">
      <h1>Checkout</h1>

      {/* Cart Summary */}
      <div className="cart-summary">
        <h2>Order Summary</h2>
        <div className="cart-items">
          {cart.items.map((item) => (
            <div key={item.id} className="cart-item">
              <span>{item.service_title}</span>
              <span>Qty: {item.quantity}</span>
              <span>₹{item.price_at_add}</span>
            </div>
          ))}
        </div>
        <div className="cart-total">
          <strong>Total: ₹{cart.total_amount}</strong>
        </div>
      </div>

      {/* Delivery Address */}
      <div className="delivery-address">
        <h2>Delivery Address</h2>
        <div className="address-form">
          <input
            type="text"
            placeholder="House Number *"
            value={deliveryAddress.house_number}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                house_number: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="Street Name *"
            value={deliveryAddress.street_name}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                street_name: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="City *"
            value={deliveryAddress.city}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                city: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="State *"
            value={deliveryAddress.state}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                state: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="Pincode *"
            value={deliveryAddress.pincode}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                pincode: e.target.value,
              })
            }
            required
          />
        </div>
      </div>

      {/* Customer Notes */}
      <div className="customer-notes">
        <h2>Special Instructions (Optional)</h2>
        <textarea
          placeholder="Any special instructions for the service provider..."
          value={customerNotes}
          onChange={(e) => setCustomerNotes(e.target.value)}
          rows={3}
        />
      </div>

      {/* Payment Method */}
      <div className="payment-method">
        <h2>Payment Method</h2>
        <div className="payment-options">
          <label>
            <input
              type="radio"
              value="razorpay"
              checked={paymentMethod === 'razorpay'}
              onChange={(e) => setPaymentMethod(e.target.value)}
            />
            Online Payment (Razorpay)
          </label>
          <label>
            <input
              type="radio"
              value="cod"
              checked={paymentMethod === 'cod'}
              onChange={(e) => setPaymentMethod(e.target.value)}
            />
            Cash on Delivery
            {paymentConfig && paymentConfig.cod_charge_percentage > 0 && (
              <span className="cod-charges">
                (Additional {paymentConfig.cod_charge_percentage}% charges apply)
              </span>
            )}
          </label>
        </div>
      </div>

      {/* Place Order Button */}
      <div className="place-order">
        <button
          onClick={handlePlaceOrder}
          disabled={loading}
          className="place-order-btn"
        >
          {loading
            ? 'Processing...'
            : paymentMethod === 'cod'
            ? 'Place COD Order'
            : 'Proceed to Payment'}
        </button>
      </div>

      <style jsx>{`
        .checkout-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }

        .cart-summary,
        .delivery-address,
        .customer-notes,
        .payment-method {
          margin-bottom: 30px;
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
        }

        .cart-item {
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid #eee;
        }

        .address-form {
          display: grid;
          gap: 15px;
        }

        .address-form input {
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 16px;
        }

        .customer-notes textarea {
          width: 100%;
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 16px;
          resize: vertical;
        }

        .payment-options {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .payment-options label {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
        }

        .cod-charges {
          color: #666;
          font-size: 14px;
        }

        .place-order-btn {
          width: 100%;
          padding: 15px;
          background-color: #007bff;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 18px;
          cursor: pointer;
          transition: background-color 0.3s;
        }

        .place-order-btn:hover:not(:disabled) {
          background-color: #0056b3;
        }

        .place-order-btn:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }

        .loading,
        .empty-cart {
          text-align: center;
          padding: 50px;
        }

        .cart-total {
          margin-top: 15px;
          padding-top: 15px;
          border-top: 2px solid #333;
          font-size: 18px;
        }
      `}</style>
    </div>
  );
};

export default Checkout;
```

## 🎯 **Key Changes Made**

### **Backend Fix:**
1. ✅ **customer_notes field** - Now allows blank values (`allow_blank=True`)

### **Frontend Improvements:**
1. ✅ **Proper error handling** - Clear error messages
2. ✅ **Form validation** - Validates all required fields
3. ✅ **COD charges display** - Shows COD fees to users
4. ✅ **Customer notes handling** - Ensures field is never undefined
5. ✅ **Loading states** - Better UX during API calls
6. ✅ **Success messages** - Clear order confirmation

## 📋 **How to Use These Files**

1. **Copy `utils/orderApi.js`** to your Next.js project
2. **Update `components/Checkout.js`** with the provided code
3. **Ensure environment variables** are set:
   ```env
   NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
   ```

## ✅ **Expected Results**

After implementing these fixes:

1. ✅ **COD orders work** - No more customer_notes validation errors
2. ✅ **Proper error handling** - Clear error messages
3. ✅ **Form validation** - Prevents invalid submissions
4. ✅ **COD charges** - Automatically calculated and displayed
5. ✅ **Better UX** - Loading states and success messages

**Your COD order system should now work perfectly! 🎉**
