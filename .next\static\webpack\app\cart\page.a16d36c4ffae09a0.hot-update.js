"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cart/page",{

/***/ "(app-pages-browser)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: function() { return /* binding */ CartProvider; },\n/* harmony export */   useCart: function() { return /* binding */ useCart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCart = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CartProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartSummary, setCartSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Fetch cart data\n    const refreshCart = async ()=>{\n        setIsLoading(true);\n        try {\n            console.log(\"Refreshing cart data, authenticated:\", isAuthenticated);\n            // Fetch cart data (which includes GST breakdown)\n            const cartData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.getCart();\n            console.log(\"Cart data received:\", cartData);\n            setCart(cartData);\n            // Use cart data for summary as well since it contains all the summary fields plus GST breakdown\n            if (cartData && cartData.id) {\n                const summaryFromCart = {\n                    id: cartData.id,\n                    sub_total: cartData.sub_total,\n                    tax_amount: cartData.tax_amount,\n                    discount_amount: cartData.discount_amount,\n                    minimum_order_fee_applied: cartData.minimum_order_fee_applied,\n                    total_amount: cartData.total_amount,\n                    coupon_code_applied: cartData.coupon_code_applied,\n                    cgst_amount: cartData.cgst_amount,\n                    sgst_amount: cartData.sgst_amount,\n                    igst_amount: cartData.igst_amount,\n                    ugst_amount: cartData.ugst_amount,\n                    service_charge: cartData.service_charge,\n                    tax_breakdown: cartData.tax_breakdown,\n                    items_count: cartData.items_count,\n                    unique_services_count: cartData.unique_services_count,\n                    updated_at: cartData.updated_at || new Date().toISOString()\n                };\n                setCartSummary(summaryFromCart);\n            } else {\n                console.log(\"Cart data is empty or has no ID, setting cart summary to null\");\n                setCartSummary(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch cart:\", error);\n            // Handle different error scenarios\n            if (error.status === 401) {\n                console.log(\"Cart fetch failed due to authentication - user might need to login\");\n            } else if (error.status === 404) {\n                console.log(\"Cart not found - initializing empty cart\");\n            }\n            // Initialize empty cart state\n            setCart(null);\n            setCartSummary(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Initialize cart on mount and when auth state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only fetch cart data if user is authenticated\n        if (isAuthenticated) {\n            refreshCart();\n        } else {\n            // Clear cart data when user is not authenticated\n            setCart(null);\n            setCartSummary(null);\n            setIsLoading(false);\n        }\n    }, [\n        isAuthenticated\n    ]);\n    const addToCart = async function(service) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        try {\n            console.log(\"Adding to cart:\", service.id, \"quantity:\", quantity, \"authenticated:\", isAuthenticated);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.addToCart(service.id, quantity);\n            console.log(\"Add to cart response:\", response);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to add to cart:\", error);\n            // Provide more specific error information\n            if (error.status === 401) {\n                throw new Error(\"Please login to add items to cart\");\n            } else if (error.status === 404) {\n                throw new Error(\"Service not found\");\n            } else if (error.status === 400) {\n                throw new Error(error.message || \"Invalid request\");\n            } else {\n                throw new Error(\"Failed to add item to cart. Please try again.\");\n            }\n        }\n    };\n    const updateCartItem = async (itemId, quantity)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.updateCartItem(itemId, quantity);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to update cart item:\", error);\n            throw error;\n        }\n    };\n    const removeCartItem = async (itemId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCartItem(itemId);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove cart item:\", error);\n            throw error;\n        }\n    };\n    const clearCart = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.clearCart();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to clear cart:\", error);\n            throw error;\n        }\n    };\n    const applyCoupon = async (couponCode)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.applyCoupon(couponCode);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to apply coupon:\", error);\n            throw error;\n        }\n    };\n    const removeCoupon = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCoupon();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove coupon:\", error);\n            throw error;\n        }\n    };\n    const getTotalItems = ()=>{\n        return (cartSummary === null || cartSummary === void 0 ? void 0 : cartSummary.items_count) || 0;\n    };\n    const value = {\n        cart,\n        cartSummary,\n        isLoading,\n        addToCart,\n        updateCartItem,\n        removeCartItem,\n        clearCart,\n        applyCoupon,\n        removeCoupon,\n        refreshCart,\n        getTotalItems\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\contexts\\\\CartContext.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CartProvider, \"F9YQ2jhR0j9KfNAPU2KBxUpgfUc=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = CartProvider;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/CartContext.tsx\n"));

/***/ })

});