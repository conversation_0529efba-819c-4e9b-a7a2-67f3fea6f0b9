'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Package, Calendar, MapPin, CreditCard, Eye, X } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/components/ui/Toaster';
import { orderApi } from '@/lib/api';
import { Order } from '@/types/api';

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [cancellingOrder, setCancellingOrder] = useState<string | null>(null);
  const [cancelReason, setCancelReason] = useState('');
  const [showCancelModal, setShowCancelModal] = useState<string | null>(null);

  const { showToast } = useToast();

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      console.log('Fetching orders...');
      const data = await orderApi.getOrders();
      console.log('Orders API response:', data);

      // Handle different response formats
      if (Array.isArray(data)) {
        setOrders(data as Order[]);
      } else if (data && data.results && Array.isArray(data.results)) {
        // Handle paginated response
        setOrders(data.results as Order[]);
      } else if (data && data.orders && Array.isArray(data.orders)) {
        // Handle wrapped response
        setOrders(data.orders as Order[]);
      } else {
        console.warn('Unexpected orders response format:', data);
        setOrders([]);
      }
    } catch (error: any) {
      console.error('Failed to fetch orders:', error);
      showToast({ type: 'error', title: 'Failed to load orders', message: error.message });
      setOrders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelOrder = async (orderNumber: string) => {
    if (!cancelReason.trim()) {
      showToast({ type: 'error', title: 'Please provide a cancellation reason' });
      return;
    }

    setCancellingOrder(orderNumber);
    try {
      await orderApi.cancelOrder(orderNumber, cancelReason);
      await fetchOrders(); // Refresh orders
      setShowCancelModal(null);
      setCancelReason('');
      showToast({ type: 'success', title: 'Order cancelled successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to cancel order', message: error.message });
    } finally {
      setCancellingOrder(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canCancelOrder = (order: Order) => {
    return ['pending', 'confirmed'].includes(order.status);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Date not set';

    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return 'Time not set';

    const timeParts = timeString.split(':');
    if (timeParts.length < 2) return timeString;

    const [hours, minutes] = timeParts;
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  if (isLoading) {
    return (
      <MainLayout>
        <ProtectedRoute>
          <LoadingPage message="Loading your orders..." />
        </ProtectedRoute>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <ProtectedRoute>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">My Orders</h1>

          {!Array.isArray(orders) || orders.length === 0 ? (
            <div className="text-center py-16">
              <Package className="h-24 w-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-900 mb-4">No orders yet</h2>
              <p className="text-gray-600 mb-8">Start booking services to see your orders here</p>
              <Link href="/" className="btn-primary">
                Browse Services
              </Link>
            </div>
          ) : (
            <div className="space-y-6">
              {Array.isArray(orders) && orders.map((order) => (
                <div key={order.id} className="card p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        Order #{order.order_number}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Placed on {order.created_at ? formatDate(order.created_at) : 'Date not available'}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(order.payment_status)}`}>
                        {order.payment_status.toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Scheduled</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(order.scheduled_date)} at {formatTime(order.scheduled_time)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Address</p>
                        <p className="text-sm text-gray-600">
                          {order.delivery_address.city}, {order.delivery_address.state}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Payment</p>
                        <p className="text-sm text-gray-600">
                          {order.payment_method === 'razorpay' ? 'Online' : 'Cash on Delivery'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div>
                      <p className="text-lg font-semibold text-gray-900">₹{order.total_amount}</p>
                      <p className="text-sm text-gray-600">Total Amount</p>
                    </div>
                    <div className="flex space-x-3">
                      <Link
                        href={`/orders/${order.order_number}`}
                        className="btn-outline flex items-center"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Link>
                      {canCancelOrder(order) && (
                        <button
                          onClick={() => setShowCancelModal(order.order_number)}
                          className="btn-danger flex items-center"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Cancel
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Cancel Order Modal */}
          {showCancelModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancel Order</h3>
                <p className="text-gray-600 mb-4">
                  Please provide a reason for cancelling this order:
                </p>
                <textarea
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  placeholder="Enter cancellation reason..."
                  className="w-full input mb-4"
                  rows={3}
                />
                <div className="flex space-x-3">
                  <LoadingButton
                    onClick={() => handleCancelOrder(showCancelModal)}
                    isLoading={cancellingOrder === showCancelModal}
                    className="flex-1 btn-danger"
                  >
                    Cancel Order
                  </LoadingButton>
                  <button
                    onClick={() => {
                      setShowCancelModal(null);
                      setCancelReason('');
                    }}
                    className="flex-1 btn-secondary"
                  >
                    Keep Order
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </ProtectedRoute>
    </MainLayout>
  );
}
