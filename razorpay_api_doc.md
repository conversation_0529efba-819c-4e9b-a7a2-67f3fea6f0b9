# Razorpay Payment Gateway API Documentation

## Overview

This document provides comprehensive API documentation for the Razorpay payment gateway integration in the Home Services Django backend. The system supports both online payments through Razorpay and Cash on Delivery (COD) with configurable partial payment options.

## Base Configuration

**Base URL**: `http://127.0.0.1:8000/api/payments/`
**Authentication**: JWT Token required for most endpoints
**Content-Type**: `application/json`

## Environment Setup

### Admin Configuration

The payment system can be configured through Django Admin:

1. **Access Admin Panel**: `/admin/payments/paymentconfiguration/`
2. **Configure Razorpay Keys**: Set test/live API keys
3. **Environment Selection**: Choose between test/live mode
4. **Payment Methods**: Enable/disable Razorpay and COD
5. **COD Settings**: Set charges and minimum order values

## API Endpoints

### 1. Get Payment Configuration

**Endpoint**: `GET /api/payments/configuration/`
**Authentication**: Not required (public endpoint)
**Description**: Get current payment configuration for frontend

```javascript
// Request
fetch('/api/payments/configuration/')

// Response
{
  "success": true,
  "configuration": {
    "enable_razorpay": true,
    "enable_cod": true,
    "cod_charge_percentage": "2.50",
    "cod_minimum_order": "100.00",
    "environment": "test",
    "razorpay_key_id": "rzp_test_XXXXXXXXXXXX"
  }
}
```

### 2. Calculate Partial Payment

**Endpoint**: `POST /api/payments/calculate-partial/`
**Authentication**: Required
**Description**: Calculate partial payment amounts for services

```javascript
// Request
{
  "items": [
    {
      "service_id": 1,
      "quantity": 2
    },
    {
      "service_id": 3,
      "quantity": 1
    }
  ]
}

// Response
{
  "success": true,
  "calculation": {
    "total_amount": "1500.00",
    "partial_payment_amount": "300.00",
    "remaining_amount": "1200.00",
    "requires_partial_payment": true,
    "items_breakdown": [
      {
        "service_id": 1,
        "service_title": "Home Cleaning",
        "quantity": 2,
        "unit_price": "500.00",
        "total_price": "1000.00",
        "requires_partial_payment": true,
        "partial_payment_amount": "200.00",
        "remaining_amount": "800.00"
      }
    ]
  }
}
```

### 3. Initiate Payment

**Endpoint**: `POST /api/payments/initiate/`
**Authentication**: Required
**Description**: Create payment transaction and Razorpay order

```javascript
// Request
{
  "order_id": "12345",
  "amount": "1500.00",
  "payment_method": "razorpay", // or "cod"
  "currency": "INR"
}

// Razorpay Response
{
  "success": true,
  "transaction_id": "TXN20241217123456ABCDEF",
  "razorpay_order_id": "order_XXXXXXXXXXXX",
  "razorpay_key_id": "rzp_test_XXXXXXXXXXXX",
  "amount": "1500.00",
  "currency": "INR",
  "order_number": "HS20241217001",
  "environment": "test"
}

// COD Response
{
  "success": true,
  "transaction_id": "TXN20241217123456ABCDEF",
  "message": "COD payment initiated successfully",
  "cod_charges": "37.50",
  "total_amount": "1537.50"
}
```

### 4. Razorpay Payment Verification

**Endpoint**: `POST /api/payments/razorpay/callback/`
**Authentication**: Required
**Description**: Verify Razorpay payment signature

```javascript
// Request
{
  "transaction_id": "TXN20241217123456ABCDEF",
  "razorpay_payment_id": "pay_XXXXXXXXXXXX",
  "razorpay_order_id": "order_XXXXXXXXXXXX",
  "razorpay_signature": "signature_string"
}

// Response
{
  "success": true,
  "message": "Payment verified successfully",
  "transaction": {
    "transaction_id": "TXN20241217123456ABCDEF",
    "status": "success",
    "amount": "1500.00",
    "gateway_payment_id": "pay_XXXXXXXXXXXX"
  }
}
```

### 5. COD Confirmation

**Endpoint**: `POST /api/payments/cod/confirm/`
**Authentication**: Required (Provider/Staff only)
**Description**: Confirm COD payment collection

```javascript
// Request
{
  "transaction_id": "TXN20241217123456ABCDEF",
  "collected_amount": "1537.50",
  "collection_notes": "Payment collected at delivery"
}

// Response
{
  "success": true,
  "message": "COD payment confirmed successfully",
  "transaction": {
    "transaction_id": "TXN20241217123456ABCDEF",
    "status": "success",
    "amount": "1537.50"
  }
}
```

### 6. Payment Status

**Endpoint**: `GET /api/payments/status/{transaction_id}/`
**Authentication**: Required
**Description**: Get payment transaction status

```javascript
// Response
{
  "success": true,
  "transaction": {
    "transaction_id": "TXN20241217123456ABCDEF",
    "order_number": "HS20241217001",
    "payment_method": "razorpay",
    "amount": "1500.00",
    "currency": "INR",
    "status": "success",
    "gateway_payment_id": "pay_XXXXXXXXXXXX",
    "created_at": "2024-12-17T12:34:56Z",
    "completed_at": "2024-12-17T12:35:30Z"
  }
}
```

## Frontend Integration Examples

### React/Next.js Integration

```javascript
// 1. Get payment configuration
const getPaymentConfig = async () => {
  const response = await fetch('/api/payments/configuration/');
  return response.json();
};

// 2. Calculate partial payment
const calculatePartialPayment = async (items) => {
  const response = await fetch('/api/payments/calculate-partial/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ items })
  });
  return response.json();
};

// 3. Initiate Razorpay payment
const initiateRazorpayPayment = async (orderData) => {
  const response = await fetch('/api/payments/initiate/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      ...orderData,
      payment_method: 'razorpay'
    })
  });
  return response.json();
};

// 4. Handle Razorpay checkout
const handleRazorpayPayment = async (paymentData) => {
  const options = {
    key: paymentData.razorpay_key_id,
    amount: paymentData.amount * 100, // Convert to paise
    currency: paymentData.currency,
    name: "Home Services",
    description: `Order ${paymentData.order_number}`,
    order_id: paymentData.razorpay_order_id,
    handler: async (response) => {
      // Verify payment
      const verifyResponse = await fetch('/api/payments/razorpay/callback/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          transaction_id: paymentData.transaction_id,
          razorpay_payment_id: response.razorpay_payment_id,
          razorpay_order_id: response.razorpay_order_id,
          razorpay_signature: response.razorpay_signature
        })
      });
      
      const result = await verifyResponse.json();
      if (result.success) {
        // Payment successful
        window.location.href = '/payment-success';
      } else {
        // Payment failed
        window.location.href = '/payment-failed';
      }
    },
    prefill: {
      name: "Customer Name",
      email: "<EMAIL>",
      contact: "9999999999"
    },
    theme: {
      color: "#3399cc"
    }
  };

  const rzp = new Razorpay(options);
  rzp.open();
};
```

## Error Handling

All API endpoints return consistent error responses:

```javascript
// Error Response Format
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Error details"]
  }
}
```

### Common Error Codes

- `400`: Bad Request - Invalid data
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `500`: Internal Server Error - Server error

## Webhook Configuration

**Webhook URL**: `https://yourdomain.com/api/payments/webhook/razorpay/`
**Method**: POST
**Events**: payment.captured, payment.failed, refund.processed

Configure webhooks in Razorpay Dashboard for real-time payment updates.

## Security Considerations

1. **API Keys**: Never expose live API keys in frontend code
2. **Signature Verification**: Always verify payment signatures server-side
3. **HTTPS**: Use HTTPS in production
4. **Authentication**: Protect all payment endpoints with proper authentication
5. **Validation**: Validate all payment amounts and order details

## Testing

### Test Credentials

Use Razorpay test mode with test cards:
- **Card Number**: 4111 1111 1111 1111
- **Expiry**: Any future date
- **CVV**: Any 3 digits

### Test Environment

Set `active_environment` to "test" in admin panel for testing.

## Postman Collection

### Environment Variables

Create these environment variables in Postman:

```
base_url: http://127.0.0.1:8000
auth_token: your_jwt_token_here
```

### Sample Requests

#### 1. Get Payment Configuration
```
GET {{base_url}}/api/payments/configuration/
```

#### 2. Calculate Partial Payment
```
POST {{base_url}}/api/payments/calculate-partial/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "items": [
    {
      "service_id": 1,
      "quantity": 1
    }
  ]
}
```

#### 3. Initiate Razorpay Payment
```
POST {{base_url}}/api/payments/initiate/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "order_id": "1",
  "amount": "500.00",
  "payment_method": "razorpay",
  "currency": "INR"
}
```

#### 4. Verify Razorpay Payment
```
POST {{base_url}}/api/payments/razorpay/callback/
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "transaction_id": "TXN20241217123456ABCDEF",
  "razorpay_payment_id": "pay_test_123456",
  "razorpay_order_id": "order_test_123456",
  "razorpay_signature": "test_signature"
}
```

## Partial Payment Workflow

### Service Configuration

1. **Admin Setup**: Configure services with partial payment requirements
2. **Percentage**: Set percentage of total amount (e.g., 20%)
3. **Fixed Amount**: Set fixed advance amount (e.g., ₹200)

### Frontend Flow

```javascript
// 1. Calculate partial payment for cart items
const cartItems = [
  { service_id: 1, quantity: 2 },
  { service_id: 3, quantity: 1 }
];

const calculation = await calculatePartialPayment(cartItems);

if (calculation.requires_partial_payment) {
  // Show partial payment option
  const partialAmount = calculation.partial_payment_amount;
  const remainingAmount = calculation.remaining_amount;

  // User can choose to pay partial or full amount
  const paymentAmount = userChoice === 'partial' ? partialAmount : calculation.total_amount;

  // Initiate payment with chosen amount
  const paymentData = await initiateRazorpayPayment({
    order_id: orderId,
    amount: paymentAmount,
    payment_method: 'razorpay'
  });

  // Handle Razorpay checkout
  handleRazorpayPayment(paymentData);
}
```

## COD Workflow

### Customer Side
```javascript
// Initiate COD payment
const codPayment = await fetch('/api/payments/initiate/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    order_id: orderId,
    amount: totalAmount,
    payment_method: 'cod'
  })
});

const result = await codPayment.json();
// result.cod_charges contains additional COD charges
// result.total_amount contains final amount including charges
```

### Provider Side (Confirmation)
```javascript
// Confirm COD collection (Provider/Staff only)
const confirmCOD = await fetch('/api/payments/cod/confirm/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${providerToken}`
  },
  body: JSON.stringify({
    transaction_id: transactionId,
    collected_amount: collectedAmount,
    collection_notes: "Payment collected successfully"
  })
});
```

## Advanced Features

### Refund Processing

```javascript
// Initiate refund (Admin only)
const refund = await fetch('/api/payments/refund/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${adminToken}`
  },
  body: JSON.stringify({
    transaction_id: transactionId,
    amount: refundAmount,
    reason: "Customer requested refund"
  })
});
```

### Transaction Listing

```javascript
// Get all transactions (Admin only)
const transactions = await fetch('/api/payments/transactions/', {
  headers: {
    'Authorization': `Bearer ${adminToken}`
  }
});
```

## Production Deployment Checklist

1. **Environment Variables**: Set production Razorpay keys
2. **HTTPS**: Ensure all endpoints use HTTPS
3. **Webhook URL**: Update webhook URL to production domain
4. **Environment**: Set `active_environment` to "live"
5. **Testing**: Test with small amounts before going live
6. **Monitoring**: Set up payment monitoring and alerts
7. **Backup**: Ensure database backups include payment data

## Support and Troubleshooting

### Common Issues

1. **Signature Verification Failed**: Check API keys and signature calculation
2. **Payment Not Captured**: Verify webhook configuration
3. **COD Charges Not Applied**: Check COD configuration in admin
4. **Partial Payment Not Calculated**: Verify service configuration

### Debug Mode

Enable Django debug mode for detailed error messages during development:
```python
DEBUG = True  # Only in development
```

### Logging

Payment transactions are logged for debugging. Check Django logs for payment-related errors.
