Home Services Platform - Complete API Documentation
Welcome to the comprehensive API documentation for your Home Services platform. This guide outlines all the endpoints available for your Next.js customer application to interact with the backend, covering authentication, service catalog, cart management, orders, payments, and provider details.
Base Configuration
Base URL: http://localhost:8000 (development) or your production domain
Authentication: JWT Bearer tokens
Content-Type: application/json
API Documentation (Swagger UI): Available at /api/docs/ (for quick testing and exploration)
1. AUTHENTICATION APPLICATION
Associated Django Models: User, Address, FailedLoginAttempt
Base URL Path: /api/auth/
Authentication Endpoints
1.1 User Registration (Staff)
Endpoint URL: /api/auth/register/
HTTP Method: POST
Description: Register staff users with an email and password.
Authentication: AllowAny
Request Body:
JSON
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirm_password": "SecurePass123!",
  "name": "<PERSON>",
  "user_type": "STAFF"
}


Response Body (201 Created):
JSO<PERSON>
{
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "user_type": "STAFF",
    "is_verified": false,
    "date_joined": "2024-01-15T10:30:00Z"
  }
}


Next.js Client Example:
JavaScript
async function registerStaff(userData) {
  const response = await fetch('http://localhost:8000/api/auth/register/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Registration failed');
  return data;
}
// Example usage:
// registerStaff({ email: "<EMAIL>", password: "StrongPass123!", confirm_password: "StrongPass123!", name: "New Staff", user_type: "STAFF" });


1.2 Mobile Registration (Customer/Provider)
Endpoint URL: /api/auth/register/mobile/
HTTP Method: POST
Description: Register customers and providers using their mobile number.
Authentication: AllowAny
Request Body:
JSON
{
  "mobile_number": "+************",
  "name": "Jane Smith",
  "user_type": "CUSTOMER"
}


Response Body (201 Created):
JSON
{
  "message": "User registered successfully. Please verify your mobile number.",
  "user": {
    "id": 2,
    "mobile_number": "+************",
    "name": "Jane Smith",
    "user_type": "CUSTOMER",
    "is_verified": false,
    "date_joined": "2024-01-15T10:35:00Z"
  }
}


Next.js Client Example:
JavaScript
async function registerMobileUser(userData) {
  const response = await fetch('http://localhost:8000/api/auth/register/mobile/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Mobile registration failed');
  return data;
}
// Example usage:
// registerMobileUser({ mobile_number: "+919988776655", name: "New Customer", user_type: "CUSTOMER" });


1.3 Email Login (Staff)
Endpoint URL: /api/auth/login/email/
HTTP Method: POST
Description: Staff login using email and password.
Authentication: AllowAny
Request Body:
JSON
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}


Response Body (200 OK):
JSON
{
  "message": "Login successful",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe",
    "user_type": "STAFF",
    "is_verified": true
  },
  "tokens": {
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}


Next.js Client Example:
JavaScript
async function loginStaff(credentials) {
  const response = await fetch('http://localhost:8000/api/auth/login/email/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Email login failed');
  return data; // Contains user info and JWT tokens
}
// Example usage:
// loginStaff({ email: "<EMAIL>", password: "SecurePass123!" });


1.4 Mobile Login (Customer/Provider)
Endpoint URL: /api/auth/login/mobile/
HTTP Method: POST
Description: Customer/Provider login using mobile number and OTP.
Authentication: AllowAny
Request Body:
JSON
{
  "mobile_number": "+************",
  "otp": "123456"
}


Response Body (200 OK):
JSON
{
  "message": "Login successful",
  "user": {
    "id": 2,
    "mobile_number": "+************",
    "name": "Jane Smith",
    "user_type": "CUSTOMER",
    "is_verified": true
  },
  "tokens": {
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}


Next.js Client Example:
JavaScript
async function loginMobileUser(credentials) {
  const response = await fetch('http://localhost:8000/api/auth/login/mobile/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Mobile login failed');
  return data; // Contains user info and JWT tokens
}
// Example usage:
// loginMobileUser({ mobile_number: "+************", otp: "123456" });


OTP Management Endpoints
1.5 Send OTP
Endpoint URL: /api/auth/otp/send/
HTTP Method: POST
Description: Sends an OTP to the specified mobile number via MSG91.
Authentication: AllowAny
Request Body:
JSON
{
  "mobile_number": "+************"
}


Response Body (200 OK):
JSON
{
  "success": true,
  "message": "OTP sent successfully",
  "mobile_number": "+************"
}


Next.js Client Example:
JavaScript
async function sendOtp(mobileNumber) {
  const response = await fetch('http://localhost:8000/api/auth/otp/send/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ mobile_number: mobileNumber }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to send OTP');
  return data;
}
// Example usage:
// sendOtp("+************");


1.6 Resend OTP
Endpoint URL: /api/auth/otp/resend/
HTTP Method: POST
Description: Resends the OTP, typically via a voice call.
Authentication: AllowAny
Request Body:
JSON
{
  "mobile_number": "+************"
}


Response Body (200 OK):
JSON
{
  "success": true,
  "message": "OTP resent via voice call",
  "mobile_number": "+************"
}


Next.js Client Example:
JavaScript
async function resendOtp(mobileNumber) {
  const response = await fetch('http://localhost:8000/api/auth/otp/resend/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ mobile_number: mobileNumber }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to resend OTP');
  return data;
}
// Example usage:
// resendOtp("+************");


1.7 Verify OTP
Endpoint URL: /api/auth/otp/verify/
HTTP Method: POST
Description: Verifies the OTP sent to the mobile number.
Authentication: AllowAny
Request Body:
JSON
{
  "mobile_number": "+************",
  "otp": "123456"
}


Response Body (200 OK):
JSON
{
  "success": true,
  "message": "OTP verified successfully",
  "mobile_number": "+************"
}


Next.js Client Example:
JavaScript
async function verifyOtp(mobileNumber, otp) {
  const response = await fetch('http://localhost:8000/api/auth/otp/verify/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ mobile_number: mobileNumber, otp: otp }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'OTP verification failed');
  return data;
}
// Example usage:
// verifyOtp("+************", "123456");


User Profile Endpoints
1.8 User Profile
Endpoint URL: /api/auth/profile/
HTTP Methods: GET, PUT
Description: Retrieve or update the authenticated user's profile.
Authentication: IsAuthenticated (JWT token required)
Headers (for authenticated requests): Authorization: Bearer <access_token>
Response Body (GET - 200 OK):
JSON
{
  "id": 2,
  "email": null,
  "mobile_number": "+************",
  "name": "Jane Smith",
  "user_type": "CUSTOMER",
  "is_verified": true,
  "profile_picture": null,
  "date_joined": "2024-01-15T10:35:00Z"
}


Request Body (PUT):
JSON
{
  "name": "Jane Smith Updated",
  "profile_picture": "base64_encoded_image_or_file_upload" // Send as multipart/form-data for actual file upload
}


Next.js Client Example (GET):
JavaScript
async function getUserProfile(accessToken) {
  const response = await fetch('http://localhost:8000/api/auth/profile/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch profile');
  return data;
}
// Example usage:
// getUserProfile(yourAccessToken);


Next.js Client Example (PUT):
JavaScript
async function updateProfile(profileData, accessToken) {
  const response = await fetch('http://localhost:8000/api/auth/profile/', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json', // Use 'multipart/form-data' if sending a file
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(profileData), // Use FormData for file uploads
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to update profile');
  return data;
}
// Example usage:
// updateProfile({ name: "Jane Doe Updated" }, yourAccessToken);


1.9 Change Password
Endpoint URL: /api/auth/change-password/
HTTP Method: POST
Description: Allows staff users to change their password.
Authentication: IsAuthenticated
Request Body:
JSON
{
  "old_password": "OldPass123!",
  "new_password": "NewPass123!",
  "confirm_password": "NewPass123!"
}


Next.js Client Example:
JavaScript
async function changePassword(passwordData, accessToken) {
  const response = await fetch('http://localhost:8000/api/auth/change-password/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(passwordData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to change password');
  return data;
}
// Example usage:
// changePassword({ old_password: "OldPass123!", new_password: "NewPass123!", confirm_password: "NewPass123!" }, yourAccessToken);


Address Management Endpoints
1.10 Address List/Create
Endpoint URL: /api/auth/addresses/
HTTP Methods: GET, POST
Description: List all addresses for the authenticated user or create a new address.
Authentication: IsAuthenticated
Request Body (POST):
JSON
{
  "address_type": "HOME",
  "street": "123 Main Street, Apartment 4B",
  "city": "Mumbai",
  "state": "Maharashtra",
  "zip_code": "400001",
  "landmark": "Near Central Mall",
  "is_default": true
}


Response Body (GET - 200 OK):
JSON
[
  {
    "id": 1,
    "address_type": "HOME",
    "street": "123 Main Street, Apartment 4B",
    "city": "Mumbai",
    "state": "Maharashtra",
    "zip_code": "400001",
    "landmark": "Near Central Mall",
    "is_default": true,
    "created_at": "2024-01-15T11:00:00Z"
  }
]


Next.js Client Example (GET):
JavaScript
async function getAddresses(accessToken) {
  const response = await fetch('http://localhost:8000/api/auth/addresses/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch addresses');
  return data;
}
// Example usage:
// getAddresses(yourAccessToken);


Next.js Client Example (POST):
JavaScript
async function createAddress(addressData, accessToken) {
  const response = await fetch('http://localhost:8000/api/auth/addresses/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(addressData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to create address');
  return data;
}
// Example usage:
// createAddress({ address_type: "WORK", street: "456 Office Rd", city: "Mumbai", state: "Maharashtra", zip_code: "400002" }, yourAccessToken);


1.11 Address Detail
Endpoint URL: /api/auth/addresses/<int:pk>/
HTTP Methods: GET, PUT, PATCH, DELETE
Description: Retrieve, update, or delete a specific address by its ID.
Authentication: IsAuthenticated
URL Path Parameters: pk: integer - Address ID
Next.js Client Example (GET):
JavaScript
async function getAddressDetail(addressId, accessToken) {
  const response = await fetch(`http://localhost:8000/api/auth/addresses/${addressId}/`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch address details');
  return data;
}
// Example usage:
// getAddressDetail(1, yourAccessToken);


Next.js Client Example (DELETE):
JavaScript
async function deleteAddress(addressId, accessToken) {
  const response = await fetch(`http://localhost:8000/api/auth/addresses/${addressId}/`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  if (!response.ok) throw new Error(`Failed to delete address: ${response.status}`);
  return; // No content returned for 204
}
// Example usage:
// deleteAddress(1, yourAccessToken);


Token Management
1.12 Token Refresh
Endpoint URL: /api/auth/token/refresh/
HTTP Method: POST
Description: Refreshes an expired access token using a valid refresh token.
Authentication: AllowAny
Request Body:
JSON
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}


Next.js Client Example:
JavaScript
async function refreshToken(refreshTokenValue) {
  const response = await fetch('http://localhost:8000/api/auth/token/refresh/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refresh: refreshTokenValue }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.detail || 'Failed to refresh token');
  return data; // Contains new access and refresh tokens
}
// Example usage:
// refreshToken(yourRefreshToken).then(tokens => console.log("New tokens:", tokens));


1.13 Logout
Endpoint URL: /api/auth/logout/
HTTP Method: POST
Description: Logs out the user by blacklisting their refresh token, invalidating both access and refresh tokens.
Authentication: IsAuthenticated
Request Body:
JSON
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}


Next.js Client Example:
JavaScript
async function logoutUser(refreshTokenValue, accessToken) {
  const response = await fetch('http://localhost:8000/api/auth/logout/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`, // Use current access token
    },
    body: JSON.stringify({ refresh: refreshTokenValue }),
  });
  if (!response.ok) throw new Error(`Logout failed: ${response.status}`);
  // Clear tokens from client-side storage after successful logout
  return; // Typically 200 OK or 204 No Content
}
// Example usage:
// logoutUser(yourRefreshToken, yourAccessToken);


2. CATALOGUE APPLICATION
Associated Django Models: Category, Service, Discount
Base URL Path: /api/catalogue/
Category Endpoints
2.1 Category List/Create
Endpoint URL: /api/catalogue/categories/
HTTP Methods: GET, POST
Description: Retrieves a list of all categories or creates a new category.
Authentication: GET (AllowAny), POST (IsAdminUser)
Query Parameters (GET):
parent (optional, integer): Filters by parent category ID. Example: ?parent=1
level (optional, integer): Filters by category nesting level. Example: ?level=0
search (optional, string): Searches in category name and description. Example: ?search=cleaning
ordering (optional, string): Sorts the results. Use name, -name, created_at, -created_at.
Request Body (POST):
JSON
{
  "name": "Home Cleaning",
  "description": "Professional home cleaning services",
  "parent": null, // Optional, for subcategories
  "image": "category_image_file" // Send as multipart/form-data for actual file upload
}


Response Body (GET - 200 OK):
JSON
[
  {
    "id": 1,
    "name": "Home Cleaning",
    "slug": "home-cleaning",
    "image": "/media/category_images/cleaning.jpg",
    "description": "Professional home cleaning services",
    "parent": null,
    "parent_name": null,
    "level": 0,
    "services_count": 5,
    "is_active": true
  }
]


Next.js Client Example (GET):
JavaScript
async function getCategories(params = {}) {
  const query = new URLSearchParams(params).toString();
  const response = await fetch(`http://localhost:8000/api/catalogue/categories/?${query}`);
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch categories');
  return data;
}
// Example usage:
// getCategories({ search: 'cleaning', level: 0 });


Next.js Client Example (POST - Admin Only):
JavaScript
async function createCategory(categoryData, accessToken) {
  const formData = new FormData();
  for (const key in categoryData) {
    formData.append(key, categoryData[key]);
  }
  const response = await fetch('http://localhost:8000/api/catalogue/categories/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      // 'Content-Type': 'multipart/form-data' is automatically set with FormData
    },
    body: formData,
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to create category');
  return data;
}
// Example usage (assuming adminAccessToken and a file object for image):
// const newCategory = { name: "Gardening", description: "Outdoor services", image: someFileObject };
// createCategory(newCategory, adminAccessToken);


2.2 Category Detail
Endpoint URL: /api/catalogue/categories/<slug:slug>/
HTTP Methods: GET, PUT, PATCH, DELETE
Description: Retrieve, update, or delete a specific category by its slug.
Authentication: GET (AllowAny), PUT/PATCH/DELETE (IsAdminUser)
URL Path Parameters: slug: string - Category slug (e.g., home-cleaning)
Response Body (GET - 200 OK):
JSON
{
  "id": 1,
  "name": "Home Cleaning",
  "slug": "home-cleaning",
  "image": "/media/category_images/cleaning.jpg",
  "description": "Professional home cleaning services",
  "parent": null,
  "level": 0,
  "children": [
    {
      "id": 2,
      "name": "Deep Cleaning",
      "slug": "deep-cleaning",
      "level": 1
    }
  ],
  "services": [
    {
      "id": 1,
      "title": "Full House Cleaning",
      "slug": "full-house-cleaning",
      "base_price": "2500.00",
      "current_price": "2000.00",
      "discount_percentage": 20.0
    }
  ],
  "services_count": 5,
  "is_active": true,
  "created_at": "2024-01-15T12:00:00Z"
}


Next.js Client Example (GET):
JavaScript
async function getCategoryDetail(slug) {
  const response = await fetch(`http://localhost:8000/api/catalogue/categories/${slug}/`);
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch category detail');
  return data;
}
// Example usage:
// getCategoryDetail('home-cleaning');


2.3 Category Tree
Endpoint URL: /api/catalogue/categories/tree/
HTTP Method: GET
Description: Retrieves the hierarchical tree structure of all categories, useful for navigation.
Authentication: AllowAny
Response Body (200 OK): (Will be a nested JSON structure representing the category hierarchy)
Next.js Client Example:
JavaScript
async function getCategoryTree() {
  const response = await fetch('http://localhost:8000/api/catalogue/categories/tree/');
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch category tree');
  return data;
}
// Example usage:
// getCategoryTree().then(tree => console.log(tree));


2.4 Category Services
Endpoint URL: /api/catalogue/categories/<slug:category_slug>/services/
HTTP Method: GET
Description: Retrieves all services belonging to a specific category, including services from its subcategories.
Authentication: AllowAny
URL Path Parameters: category_slug: string - The slug of the category (e.g., home-cleaning)
Response Body (200 OK): (Similar to Service List response, filtered by category)
Next.js Client Example:
JavaScript
async function getServicesByCategory(categorySlug) {
  const response = await fetch(`http://localhost:8000/api/catalogue/categories/${categorySlug}/services/`);
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch category services');
  return data;
}
// Example usage:
// getServicesByCategory('home-cleaning');


Service Endpoints
2.5 Service List/Create
Endpoint URL: /api/catalogue/services/
HTTP Methods: GET, POST
Description: Retrieves a list of all services or creates a new service.
Authentication: GET (AllowAny), POST (IsAdminUser)
Query Parameters (GET):
category (optional, integer): Filters by category ID.
category__parent (optional, integer): Filters by parent category ID.
search (optional, string): Searches in service title and description.
ordering (optional, string): Sorts results by title, base_price, created_at (prefix with - for descending).
Request Body (POST):
JSON
{
  "title": "Full House Deep Cleaning",
  "description": "Complete deep cleaning service for entire house",
  "category": 1, // Category ID
  "base_price": "3000.00",
  "discount_price": "2500.00", // Optional
  "time_to_complete": "04:00:00", // Format HH:MM:SS
  "image": "service_image_file" // Send as multipart/form-data
}


Response Body (GET - 200 OK):
JSON
[
  {
    "id": 1,
    "title": "Full House Deep Cleaning",
    "slug": "full-house-deep-cleaning",
    "image": "/media/service_images/cleaning.jpg",
    "base_price": "3000.00",
    "discount_price": "2500.00",
    "current_price": "2500.00",
    "discount_percentage": 16.67,
    "category_name": "Home Cleaning",
    "time_to_complete": "04:00:00"
  }
]


Next.js Client Example (GET):
JavaScript
async function getServices(params = {}) {
  const query = new URLSearchParams(params).toString();
  const response = await fetch(`http://localhost:8000/api/catalogue/services/?${query}`);
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch services');
  return data;
}
// Example usage:
// getServices({ category: 1, search: 'deep' });


2.6 Service Detail
Endpoint URL: /api/catalogue/services/<slug:slug>/
HTTP Methods: GET, PUT, PATCH, DELETE
Description: Retrieve, update, or delete a specific service by its slug.
Authentication: GET (AllowAny), PUT/PATCH/DELETE (IsAdminUser)
URL Path Parameters: slug: string - Service slug (e.g., full-house-deep-cleaning)
Response Body (GET - 200 OK):
JSON
{
  "id": 1,
  "title": "Full House Deep Cleaning",
  "slug": "full-house-deep-cleaning",
  "image": "/media/service_images/cleaning.jpg",
  "description": "Complete deep cleaning service for entire house including all rooms, kitchen, and bathrooms",
  "base_price": "3000.00",
  "discount_price": "2500.00",
  "current_price": "2500.00",
  "discount_percentage": 16.67,
  "time_to_complete": "04:00:00",
  "category": 1,
  "category_name": "Home Cleaning",
  "is_active": true,
  "created_at": "2024-01-15T12:30:00Z",
  "updated_at": "2024-01-15T12:30:00Z"
}


Next.js Client Example (GET):
JavaScript
async function getServiceDetail(slug) {
  const response = await fetch(`http://localhost:8000/api/catalogue/services/${slug}/`);
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch service detail');
  return data;
}
// Example usage:
// getServiceDetail('full-house-deep-cleaning');


2.7 Service Search
Endpoint URL: /api/catalogue/services/search/
HTTP Method: GET
Description: Provides an advanced search for services with multiple filter options.
Authentication: AllowAny
Query Parameters:
q (optional, string): The main search query.
category (optional, integer): Filters by category ID.
min_price (optional, decimal): Filters by minimum price.
max_price (optional, decimal): Filters by maximum price.
Response Body (200 OK): (Similar to Service List response, filtered by search criteria)
Next.js Client Example:
JavaScript
async function searchServices(params = {}) {
  const query = new URLSearchParams(params).toString();
  const response = await fetch(`http://localhost:8000/api/catalogue/services/search/?${query}`);
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to search services');
  return data;
}
// Example usage:
// searchServices({ q: 'cleaning', category: 1, min_price: 2000 });


3. CART APPLICATION
Associated Django Models: Cart, CartItem
Base URL Path: /api/cart/
Cart Management Endpoints
3.1 Cart Detail
Endpoint URL: /api/cart/
HTTP Method: GET
Description: Retrieves the current user's cart details, including all items, totals, and applied coupons. Supports both authenticated and anonymous users.
Authentication: AllowAny (Client should send JWT token if authenticated, or rely on session for anonymous)
Response Body (200 OK):
JSON
{
  "id": 1,
  "user": 2, // null for anonymous cart
  "session_key": null, // or a session key for anonymous cart
  "created_at": "2024-01-15T13:00:00Z",
  "updated_at": "2024-01-15T13:15:00Z",
  "is_active": true,
  "sub_total": "5000.00",
  "tax_amount": "900.00",
  "discount_amount": "500.00",
  "minimum_order_fee_applied": "0.00",
  "total_amount": "5400.00",
  "coupon_code_applied": "SAVE20",
  "items": [
    {
      "id": 1,
      "service": 1,
      "service_title": "Full House Deep Cleaning",
      "service_slug": "full-house-deep-cleaning",
      "service_image": "/media/service_images/cleaning.jpg",
      "service_category": "Home Cleaning",
      "quantity": 2,
      "price_at_add": "2500.00",
      "discount_at_add": "0.00",
      "current_service_price": "2500.00",
      "total_price": "5000.00",
      "savings": "0.00",
      "added_at": "2024-01-15T13:00:00Z"
    }
  ],
  "items_count": 2,
  "unique_services_count": 1
}


Next.js Client Example:
JavaScript
async function getCartDetail(accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch('http://localhost:8000/api/cart/', {
    method: 'GET',
    headers: headers,
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch cart');
  return data;
}
// Example usage:
// getCartDetail(yourAccessToken); // For authenticated user
// getCartDetail(); // For anonymous user (session managed by browser cookies)


3.2 Cart Summary
Endpoint URL: /api/cart/summary/
HTTP Method: GET
Description: Retrieves a concise summary of the cart, including totals and counts, without listing individual items.
Authentication: AllowAny
Response Body (200 OK):
JSON
{
  "id": 1,
  "sub_total": "5000.00",
  "tax_amount": "900.00",
  "discount_amount": "500.00",
  "minimum_order_fee_applied": "0.00",
  "total_amount": "5400.00",
  "coupon_code_applied": "SAVE20",
  "items_count": 2,
  "unique_services_count": 1,
  "updated_at": "2024-01-15T13:15:00Z"
}


Next.js Client Example:
JavaScript
async function getCartSummary(accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch('http://localhost:8000/api/cart/summary/', {
    method: 'GET',
    headers: headers,
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch cart summary');
  return data;
}
// Example usage:
// getCartSummary(yourAccessToken);


Cart Operations
3.3 Add to Cart
Endpoint URL: /api/cart/add/
HTTP Method: POST
Description: Adds a specified service with a given quantity to the user's cart. If the service already exists, the quantity is updated.
Authentication: AllowAny
Request Body:
JSON
{
  "service": 1, // Service ID
  "quantity": 2
}


Response Body (200 OK):
JSON
{
  "success": true,
  "message": "Service added to cart successfully",
  "cart_item": {
    "id": 1,
    "service": 1,
    "service_title": "Full House Deep Cleaning",
    "quantity": 2,
    "total_price": "5000.00"
  }
}


Next.js Client Example:
JavaScript
async function addToCart(serviceId, quantity, accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch('http://localhost:8000/api/cart/add/', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({ service: serviceId, quantity: quantity }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to add to cart');
  return data;
}
// Example usage:
// addToCart(1, 2, yourAccessToken);


3.4 Update Cart Item
Endpoint URL: /api/cart/items/<int:item_id>/update/
HTTP Methods: PUT, PATCH
Description: Updates the quantity of a specific cart item.
Authentication: AllowAny
URL Path Parameters: item_id: integer - The ID of the cart item to update.
Request Body:
JSON
{
  "quantity": 3
}


Next.js Client Example:
JavaScript
async function updateCartItem(itemId, newQuantity, accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch(`http://localhost:8000/api/cart/items/${itemId}/update/`, {
    method: 'PUT', // or 'PATCH'
    headers: headers,
    body: JSON.stringify({ quantity: newQuantity }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to update cart item');
  return data;
}
// Example usage:
// updateCartItem(1, 3, yourAccessToken);


3.5 Remove Cart Item
Endpoint URL: /api/cart/items/<int:item_id>/remove/
HTTP Method: DELETE
Description: Removes a specific item from the cart.
Authentication: AllowAny
URL Path Parameters: item_id: integer - The ID of the cart item to remove.
Next.js Client Example:
JavaScript
async function removeCartItem(itemId, accessToken = null) {
  const headers = {}; // No Content-Type needed for DELETE without body
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch(`http://localhost:8000/api/cart/items/${itemId}/remove/`, {
    method: 'DELETE',
    headers: headers,
  });
  if (!response.ok) throw new Error(`Failed to remove cart item: ${response.status}`);
  return; // Typically 204 No Content
}
// Example usage:
// removeCartItem(1, yourAccessToken);


3.6 Clear Cart
Endpoint URL: /api/cart/clear/
HTTP Method: POST
Description: Removes all items from the current user's cart.
Authentication: AllowAny
Next.js Client Example:
JavaScript
async function clearCart(accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch('http://localhost:8000/api/cart/clear/', {
    method: 'POST',
    headers: headers,
  });
  if (!response.ok) throw new Error(`Failed to clear cart: ${response.status}`);
  return; // Typically 200 OK or 204 No Content
}
// Example usage:
// clearCart(yourAccessToken);


Coupon Management (within Cart)
3.7 Apply Coupon
Endpoint URL: /api/cart/coupon/apply/
HTTP Method: POST
Description: Applies a given coupon code to the current cart, recalculating totals with the discount.
Authentication: AllowAny
Request Body:
JSON
{
  "coupon_code": "SAVE20"
}


Next.js Client Example:
JavaScript
async function applyCartCoupon(couponCode, accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch('http://localhost:8000/api/cart/coupon/apply/', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({ coupon_code: couponCode }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to apply coupon');
  return data; // Returns updated cart summary
}
// Example usage:
// applyCartCoupon("SAVE20", yourAccessToken);


3.8 Remove Coupon
Endpoint URL: /api/cart/coupon/remove/
HTTP Method: POST
Description: Removes any applied coupon from the cart, reverting totals to pre-discount values.
Authentication: AllowAny
Next.js Client Example:
JavaScript
async function removeCartCoupon(accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch('http://localhost:8000/api/cart/coupon/remove/', {
    method: 'POST',
    headers: headers,
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to remove coupon');
  return data; // Returns updated cart summary
}
// Example usage:
// removeCartCoupon(yourAccessToken);


4. COUPONS APPLICATION
Associated Django Models: Coupon, UsedCoupon, CouponApplication
Base URL Path: /api/coupons/
Note: Most endpoints in this section are likely for admin use, but validate and apply might be used by customers depending on your logic.
4.1 Coupon List/Create
Endpoint URL: /api/coupons/
HTTP Methods: GET, POST
Description: Retrieves a list of all coupons or creates a new coupon.
Authentication: IsAdminUser (for both GET and POST methods based on typical coupon management)
Request Body (POST):
JSON
{
  "code": "SAVE20",
  "name": "20% Off on All Services",
  "discount_type": "percentage", // "percentage" or "fixed"
  "discount_value": "20.00",
  "minimum_order_amount": "1000.00", // Optional
  "maximum_discount_amount": "500.00", // Optional
  "usage_limit": 100, // Total uses across all users
  "usage_limit_per_user": 1, // Uses per user
  "valid_from": "2024-01-15T00:00:00Z",
  "valid_until": "2024-02-15T23:59:59Z",
  "is_active": true
}


Next.js Client Example (GET - Admin Only):
JavaScript
async function getCoupons(adminAccessToken) {
  const response = await fetch('http://localhost:8000/api/coupons/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminAccessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch coupons');
  return data;
}
// Example usage:
// getCoupons(adminAccessToken);


4.2 Coupon Detail
Endpoint URL: /api/coupons/<str:code>/
HTTP Methods: GET, PUT, PATCH, DELETE
Description: Retrieve, update, or delete a specific coupon by its code.
Authentication: IsAdminUser
URL Path Parameters: code: string - Coupon code (e.g., SAVE20)
Next.js Client Example (GET - Admin Only):
JavaScript
async function getCouponDetail(couponCode, adminAccessToken) {
  const response = await fetch(`http://localhost:8000/api/coupons/${couponCode}/`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminAccessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch coupon detail');
  return data;
}
// Example usage:
// getCouponDetail("SAVE20", adminAccessToken);


4.3 Validate Coupon
Endpoint URL: /api/coupons/validate/
HTTP Method: POST
Description: Validates a given coupon code against a specified cart amount to check eligibility.
Authentication: AllowAny
Request Body:
JSON
{
  "coupon_code": "SAVE20",
  "cart_amount": "2500.00"
}


Response Body (200 OK - Valid):
JSON
{
  "is_valid": true,
  "message": "Coupon is valid",
  "discount_amount": "500.00",
  "applied_coupon": {
    "code": "SAVE20",
    "name": "20% Off on All Services"
  }
}


Response Body (400 Bad Request - Invalid):
JSON
{
  "is_valid": false,
  "message": "Coupon is expired or not applicable"
}


Next.js Client Example:
JavaScript
async function validateCoupon(couponCode, cartAmount) {
  const response = await fetch('http://localhost:8000/api/coupons/validate/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ coupon_code: couponCode, cart_amount: cartAmount }),
  });
  const data = await response.json();
  // Note: This endpoint might return 200 OK even if not valid, with is_valid: false
  return data;
}
// Example usage:
// validateCoupon("SAVE20", 2500.00);


4.4 Apply Coupon
Endpoint URL: /api/coupons/apply/
HTTP Method: POST
Description: Applies a coupon to the user's cart and calculates the resulting discount. (This might be redundant with 3.7 Apply Coupon if that's the primary way).
Authentication: AllowAny
Request Body: Similar to 3.7 Apply Coupon
Next.js Client Example: (Similar to 3.7 Apply Coupon)
JavaScript
async function applyCouponDirectly(couponCode, accessToken = null) {
  const headers = { 'Content-Type': 'application/json' };
  if (accessToken) headers['Authorization'] = `Bearer ${accessToken}`;

  const response = await fetch('http://localhost:8000/api/coupons/apply/', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({ coupon_code: couponCode }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to apply coupon');
  return data;
}
// Example usage:
// applyCouponDirectly("WELCOME10", yourAccessToken);


4.5 Coupon Usage
Endpoint URL: /api/coupons/usage/
HTTP Method: GET
Description: Retrieves the usage history for all coupons.
Authentication: IsAdminUser
Next.js Client Example (Admin Only):
JavaScript
async function getCouponUsage(adminAccessToken) {
  const response = await fetch('http://localhost:8000/api/coupons/usage/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminAccessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch coupon usage');
  return data;
}
// Example usage:
// getCouponUsage(adminAccessToken);


5. ORDERS APPLICATION
Associated Django Models: Order, OrderItem, OrderStatusHistory, OrderReschedule
Base URL Path: /api/orders/
5.1 Order List/Create
Endpoint URL: /api/orders/
HTTP Methods: GET, POST
Description: Retrieves a list of orders for the authenticated user or creates a new order from the current cart.
Authentication: IsAuthenticated
Request Body (POST):
JSON
{
  "delivery_address": { // Can be existing address ID or full new address
    "street": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "zip_code": "400001",
    "landmark": "Near Central Mall", // Optional
    "address_type": "HOME" // Optional, if creating new address
  },
  "scheduled_date": "2024-01-20", // YYYY-MM-DD
  "scheduled_time": "10:00:00", // HH:MM:SS
  "payment_method": "razorpay", // or "cash_on_delivery"
  "special_instructions": "Please call before arriving" // Optional
}


Response Body (GET - 200 OK):
JSON
[
  {
    "id": 1,
    "order_number": "HS20240115001",
    "customer": 2, // Customer User ID
    "status": "confirmed",
    "payment_status": "paid",
    "payment_method": "razorpay",
    "sub_total": "5000.00",
    "tax_amount": "900.00",
    "discount_amount": "500.00",
    "total_amount": "5400.00",
    "scheduled_date": "2024-01-20",
    "scheduled_time": "10:00:00",
    "assigned_provider": null,
    "created_at": "2024-01-15T14:00:00Z"
  }
]


Next.js Client Example (GET):
JavaScript
async function getOrders(accessToken) {
  const response = await fetch('http://localhost:8000/api/orders/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch orders');
  return data;
}
// Example usage:
// getOrders(yourAccessToken);


Next.js Client Example (POST):
JavaScript
async function createOrder(orderData, accessToken) {
  const response = await fetch('http://localhost:8000/api/orders/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(orderData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to create order');
  return data;
}
// Example usage:
// createOrder({ delivery_address: { street: "123 Main St", city: "Mumbai", state: "MH", zip_code: "400001" }, scheduled_date: "2024-06-25", scheduled_time: "14:00:00", payment_method: "razorpay" }, yourAccessToken);


5.2 Order Detail
Endpoint URL: /api/orders/<str:order_number>/
HTTP Methods: GET, PUT, PATCH
Description: Retrieve or update details of a specific order.
Authentication: IsAuthenticated
URL Path Parameters: order_number: string - The unique order number (e.g., HS20240115001)
Next.js Client Example (GET):
JavaScript
async function getOrderDetail(orderNumber, accessToken) {
  const response = await fetch(`http://localhost:8000/api/orders/${orderNumber}/`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch order detail');
  return data;
}
// Example usage:
// getOrderDetail("HS20240115001", yourAccessToken);


5.3 Update Order Status
Endpoint URL: /api/orders/<str:order_number>/status/
HTTP Method: POST
Description: Updates the status of an order (e.g., in_progress, completed).
Authentication: IsAuthenticated (Provider/Admin only)
Request Body:
JSON
{
  "status": "in_progress", // e.g., "pending", "confirmed", "in_progress", "completed", "cancelled"
  "notes": "Work started" // Optional
}


Next.js Client Example (Provider/Admin Only):
JavaScript
async function updateOrderStatus(orderNumber, statusData, accessToken) {
  const response = await fetch(`http://localhost:8000/api/orders/${orderNumber}/status/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(statusData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to update order status');
  return data;
}
// Example usage:
// updateOrderStatus("HS20240115001", { status: "in_progress" }, providerOrAdminAccessToken);


5.4 Cancel Order
Endpoint URL: /api/orders/<str:order_number>/cancel/
HTTP Method: POST
Description: Cancels an order.
Authentication: IsAuthenticated
Request Body:
JSON
{
  "cancellation_reason": "Customer requested cancellation" // Required
}


Next.js Client Example:
JavaScript
async function cancelOrder(orderNumber, reason, accessToken) {
  const response = await fetch(`http://localhost:8000/api/orders/${orderNumber}/cancel/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify({ cancellation_reason: reason }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to cancel order');
  return data;
}
// Example usage:
// cancelOrder("HS20240115001", "Changed my mind", yourAccessToken);


5.5 Assign Provider
Endpoint URL: /api/orders/<str:order_number>/assign-provider/
HTTP Method: POST
Description: Assigns a provider to an order.
Authentication: IsAdminUser
Request Body:
JSON
{
  "provider_id": 5 // Provider User ID
}


Next.js Client Example (Admin Only):
JavaScript
async function assignProvider(orderNumber, providerId, adminAccessToken) {
  const response = await fetch(`http://localhost:8000/api/orders/${orderNumber}/assign-provider/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminAccessToken}`,
    },
    body: JSON.stringify({ provider_id: providerId }),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to assign provider');
  return data;
}
// Example usage:
// assignProvider("HS20240115001", 5, adminAccessToken);


5.6 Order Dashboard
Endpoint URL: /api/orders/dashboard/
HTTP Method: GET
Description: Retrieves order statistics and dashboard data. This might be user-specific (customer's own stats) or global (admin dashboard).
Authentication: IsAuthenticated (Access might vary based on user_type)
Response Body (200 OK): (Example - actual fields will vary)
JSON
{
  "total_orders": 100,
  "pending_orders": 15,
  "completed_orders": 80,
  "cancelled_orders": 5,
  "revenue": "150000.00",
  "services_most_ordered": [
    {"service_name": "Deep Cleaning", "count": 30},
    {"service_name": "Plumbing Repair", "count": 20}
  ]
}


Next.js Client Example:
JavaScript
async function getOrderDashboard(accessToken) {
  const response = await fetch('http://localhost:8000/api/orders/dashboard/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch dashboard data');
  return data;
}
// Example usage:
// getOrderDashboard(yourAccessToken);


6. PAYMENTS APPLICATION
Associated Django Models: PaymentTransaction, RefundTransaction
Base URL Path: /api/payments/
6.1 Initiate Payment
Endpoint URL: /api/payments/initiate/
HTTP Method: POST
Description: Initiates a payment for a given order, typically returning details needed for the frontend to open a payment gateway (e.g., Razorpay checkout).
Authentication: IsAuthenticated
Request Body:
JSON
{
  "order_id": "HS20240115001", // The order number to associate payment with
  "payment_method": "razorpay", // e.g., "razorpay", "stripe", "cash_on_delivery"
  "amount": "5400.00" // The total amount for the order
}


Response Body (200 OK):
JSON
{
  "success": true,
  "transaction_id": "TXN20240115140001ABC123", // Internal transaction ID
  "payment_gateway_data": {
    "razorpay_order_id": "order_xyz123", // Gateway-specific order ID
    "amount": 540000, // Amount in smallest currency unit (e.g., paise for INR)
    "currency": "INR",
    "key": "rzp_test_key" // Gateway public key
  }
}


Next.js Client Example:
JavaScript
async function initiatePayment(paymentDetails, accessToken) {
  const response = await fetch('http://localhost:8000/api/payments/initiate/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(paymentDetails),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to initiate payment');
  return data;
}
// Example usage:
// initiatePayment({ order_id: "HS20240115001", payment_method: "razorpay", amount: "5400.00" }, yourAccessToken);
// After this, use payment_gateway_data to open Razorpay checkout on frontend.


6.2 Razorpay Callback
Endpoint URL: /api/payments/razorpay/callback/
HTTP Method: POST
Description: This endpoint is typically hit by the Razorpay webhook or by the frontend after a successful payment to handle the payment gateway's response and update the order/payment status on the backend.
Authentication: AllowAny (This endpoint usually relies on signature verification for security, not JWT)
Request Body: (Sent by Razorpay or frontend after checkout)
JSON
{
  "razorpay_payment_id": "pay_xyz123",
  "razorpay_order_id": "order_xyz123",
  "razorpay_signature": "signature_hash" // Used for verification
}


Next.js Client Example: (This is usually called by the frontend after Razorpay's checkout widget completes)
JavaScript
async function handleRazorpayCallback(callbackData) {
  const response = await fetch('http://localhost:8000/api/payments/razorpay/callback/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(callbackData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Payment callback failed');
  return data;
}
// Example usage (assuming Razorpay checkout provides these details):
// handleRazorpayCallback({ razorpay_payment_id: "pay_xyz123", razorpay_order_id: "order_xyz123", razorpay_signature: "signature_hash" });


6.3 Payment Status
Endpoint URL: /api/payments/status/<str:transaction_id>/
HTTP Method: GET
Description: Retrieves the status of a specific payment transaction.
Authentication: IsAuthenticated
URL Path Parameters: transaction_id: string - The internal transaction ID.
Response Body (200 OK):
JSON
{
  "transaction_id": "TXN20240115140001ABC123",
  "order_number": "HS20240115001",
  "amount": "5400.00",
  "status": "success", // e.g., "pending", "success", "failed", "refunded"
  "payment_method": "razorpay",
  "created_at": "2024-01-15T14:00:00Z",
  "updated_at": "2024-01-15T14:05:00Z"
}


Next.js Client Example:
JavaScript
async function getPaymentStatus(transactionId, accessToken) {
  const response = await fetch(`http://localhost:8000/api/payments/status/${transactionId}/`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch payment status');
  return data;
}
// Example usage:
// getPaymentStatus("TXN20240115140001ABC123", yourAccessToken);


6.4 Verify Payment
Endpoint URL: /api/payments/verify/
HTTP Method: POST
Description: Verifies the status of a payment, typically used after a payment attempt to confirm its final state with the backend.
Authentication: IsAuthenticated
Request Body: (May vary, typically includes gateway-specific IDs)
JSON
{
  "razorpay_order_id": "order_xyz123",
  "razorpay_payment_id": "pay_xyz123"
}


Response Body (200 OK):
JSON
{
  "success": true,
  "message": "Payment verified successfully",
  "status": "success",
  "transaction_id": "TXN20240115140001ABC123"
}


Next.js Client Example:
JavaScript
async function verifyPayment(verificationData, accessToken) {
  const response = await fetch('http://localhost:8000/api/payments/verify/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(verificationData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to verify payment');
  return data;
}
// Example usage:
// verifyPayment({ razorpay_order_id: "order_xyz123", razorpay_payment_id: "pay_xyz123" }, yourAccessToken);


6.5 Initiate Refund
Endpoint URL: /api/payments/refund/
HTTP Method: POST
Description: Initiates a refund for a previously successful payment.
Authentication: IsAuthenticated (Likely restricted to Admin or specific roles)
Request Body:
JSON
{
  "transaction_id": "TXN20240115140001ABC123", // The internal transaction ID
  "refund_amount": "5400.00", // Amount to refund (optional, defaults to full)
  "reason": "Order cancelled by customer" // Required reason for refund
}


Next.js Client Example (Admin/Authorized User Only):
JavaScript
async function initiateRefund(refundData, accessToken) {
  const response = await fetch('http://localhost:8000/api/payments/refund/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(refundData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to initiate refund');
  return data;
}
// Example usage:
// initiateRefund({ transaction_id: "TXN20240115140001ABC123", refund_amount: "5400.00", reason: "Product returned" }, adminAccessToken);


7. PROVIDERS APPLICATION
Associated Django Models: ProviderProfile, ProviderDocument, ProviderBankDetails, ProviderAvailability, ProviderPayoutRequest
Base URL Path: /api/providers/
Note: Many of these endpoints are primarily for the provider's own management, but some might be relevant for customer-facing display (e.g., viewing provider profiles if exposed).
7.1 Provider Profile
Endpoint URL: /api/providers/profile/
HTTP Method: GET
Description: Retrieves the authenticated provider's profile details.
Authentication: IsAuthenticated (Provider only)
Response Body (200 OK):
JSON
{
  "id": 1,
  "user": 3,
  "user_name": "John Provider",
  "user_mobile": "+************",
  "business_name": "John's Cleaning Services",
  "business_type": "individual", // or "company"
  "years_of_experience": 5,
  "specializations": [1, 2], // List of service Category IDs
  "specializations_names": ["Home Cleaning", "Deep Cleaning"],
  "service_areas": ["Mumbai", "Navi Mumbai"],
  "working_hours": {
    "monday": {"start": "09:00", "end": "18:00"},
    "tuesday": {"start": "09:00", "end": "18:00"},
    "wednesday": {"start": null, "end": null}, // Example for off day
    "thursday": {"start": "09:00", "end": "18:00"},
    "friday": {"start": "09:00", "end": "18:00"},
    "saturday": {"start": "10:00", "end": "15:00"},
    "sunday": {"start": null, "end": null}
  },
  "verification_status": "verified", // e.g., "pending", "verified", "rejected"
  "average_rating": "4.50",
  "total_reviews": 25,
  "total_orders_completed": 50,
  "is_available": true,
  "accepts_new_orders": true
}


Next.js Client Example:
JavaScript
async function getProviderProfile(accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/profile/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch provider profile');
  return data;
}
// Example usage:
// getProviderProfile(providerAccessToken);


7.2 Update Provider Profile
Endpoint URL: /api/providers/profile/update/
HTTP Methods: PUT, PATCH
Description: Updates the authenticated provider's profile information.
Authentication: IsAuthenticated (Provider only)
Request Body: (Contains fields to be updated, e.g.)
JSON
{
  "business_name": "John's Premier Cleaning",
  "years_of_experience": 6,
  "service_areas": ["Mumbai", "Thane", "Navi Mumbai"],
  "working_hours": {
    "monday": {"start": "08:00", "end": "17:00"}
  }
}


Next.js Client Example:
JavaScript
async function updateProviderProfile(profileData, accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/profile/update/', {
    method: 'PUT', // or 'PATCH'
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(profileData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to update provider profile');
  return data;
}
// Example usage:
// updateProviderProfile({ business_name: "Elite Cleaners" }, providerAccessToken);


7.3 Provider Documents
Endpoint URL: /api/providers/documents/
HTTP Method: GET
Description: Retrieves a list of all documents uploaded by the authenticated provider.
Authentication: IsAuthenticated (Provider only)
Response Body (200 OK): (Example)
JSON
[
  {
    "id": 1,
    "document_type": "aadhaar",
    "document_number": "1234-5678-9012",
    "document_file": "/media/documents/aadhaar_john.pdf",
    "verification_status": "pending", // e.g., "pending", "verified", "rejected"
    "uploaded_at": "2024-01-16T09:00:00Z"
  }
]


Next.js Client Example:
JavaScript
async function getProviderDocuments(accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/documents/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch provider documents');
  return data;
}
// Example usage:
// getProviderDocuments(providerAccessToken);


7.4 Upload Document
Endpoint URL: /api/providers/documents/upload/
HTTP Method: POST
Description: Uploads a verification document for the provider.
Authentication: IsAuthenticated (Provider only)
Request Body (multipart/form-data):
JSON
// This is a conceptual JSON; actual request will be FormData
{
  "document_type": "aadhaar", // e.g., "aadhaar", "pan", "driving_license"
  "document_file": "<file_upload>", // Actual file content
  "document_number": "1234-5678-9012"
}


Next.js Client Example:
JavaScript
async function uploadProviderDocument(documentData, file, accessToken) {
  const formData = new FormData();
  formData.append('document_type', documentData.document_type);
  formData.append('document_number', documentData.document_number);
  formData.append('document_file', file); // 'file' should be a File object

  const response = await fetch('http://localhost:8000/api/providers/documents/upload/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      // 'Content-Type': 'multipart/form-data' is automatically set with FormData
    },
    body: formData,
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to upload document');
  return data;
}
// Example usage:
// const documentFile = event.target.files[0]; // From an input type="file"
// uploadProviderDocument({ document_type: "pan", document_number: "**********" }, documentFile, providerAccessToken);


7.5 Bank Details
Endpoint URL: /api/providers/bank-details/
HTTP Method: GET
Description: Retrieves the authenticated provider's bank details.
Authentication: IsAuthenticated (Provider only)
Response Body (200 OK):
JSON
{
  "id": 1,
  "account_holder_name": "John Provider",
  "account_number": "**********",
  "ifsc_code": "HDFC0001234",
  "bank_name": "HDFC Bank",
  "branch_name": "Mumbai Main Branch",
  "upi_id": "john@paytm", // Optional
  "is_verified": false
}


Next.js Client Example:
JavaScript
async function getProviderBankDetails(accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/bank-details/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch bank details');
  return data;
}
// Example usage:
// getProviderBankDetails(providerAccessToken);


7.6 Update Bank Details
Endpoint URL: /api/providers/bank-details/update/
HTTP Methods: POST, PUT
Description: Adds or updates the authenticated provider's bank details.
Authentication: IsAuthenticated (Provider only)
Request Body:
JSON
{
  "account_holder_name": "John Provider",
  "account_number": "**********",
  "ifsc_code": "HDFC0001234",
  "bank_name": "HDFC Bank",
  "branch_name": "Mumbai Main Branch",
  "upi_id": "john@paytm" // Optional
}


Next.js Client Example:
JavaScript
async function updateProviderBankDetails(bankDetails, accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/bank-details/update/', {
    method: 'POST', // or 'PUT'
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(bankDetails),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to update bank details');
  return data;
}
// Example usage:
// updateProviderBankDetails({ account_number: "**********", ifsc_code: "ICIC0005678", bank_name: "ICICI Bank" }, providerAccessToken);


7.7 Provider Availability
Endpoint URL: /api/providers/availability/
HTTP Method: GET
Description: Retrieves the authenticated provider's availability slots.
Authentication: IsAuthenticated (Provider only)
Response Body (200 OK): (Example structure)
JSON
[
  {
    "id": 1,
    "date": "2024-01-20",
    "start_time": "09:00:00",
    "end_time": "13:00:00",
    "is_booked": false
  },
  {
    "id": 2,
    "date": "2024-01-20",
    "start_time": "14:00:00",
    "end_time": "18:00:00",
    "is_booked": true
  }
]


Next.js Client Example:
JavaScript
async function getProviderAvailability(accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/availability/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch availability');
  return data;
}
// Example usage:
// getProviderAvailability(providerAccessToken);


7.8 Update Availability
Endpoint URL: /api/providers/availability/update/
HTTP Method: POST
Description: Updates the authenticated provider's availability slots.
Authentication: IsAuthenticated (Provider only)
Request Body: (Example, could be a list of slots or changes)
JSON
{
  "date": "2024-01-20",
  "slots": [
    {"start_time": "09:00:00", "end_time": "13:00:00"},
    {"start_time": "14:00:00", "end_time": "18:00:00"}
  ],
  "clear_existing": true // Optional, to clear all existing slots for that date first
}


Next.js Client Example:
JavaScript
async function updateProviderAvailability(availabilityData, accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/availability/update/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(availabilityData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to update availability');
  return data;
}
// Example usage:
// updateProviderAvailability({ date: "2024-06-26", slots: [{start_time: "09:00:00", end_time: "17:00:00"}] }, providerAccessToken);


7.9 Payout Requests
Endpoint URL: /api/providers/payouts/
HTTP Method: GET
Description: Retrieves a list of payout requests made by the authenticated provider.
Authentication: IsAuthenticated (Provider only)
Response Body (200 OK): (Example)
JSON
[
  {
    "id": 1,
    "amount": "1500.00",
    "request_date": "2024-01-25T10:00:00Z",
    "status": "pending", // e.g., "pending", "completed", "rejected"
    "processed_date": null
  }
]


Next.js Client Example:
JavaScript
async function getPayoutRequests(accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/payouts/', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to fetch payout requests');
  return data;
}
// Example usage:
// getPayoutRequests(providerAccessToken);


7.10 Request Payout
Endpoint URL: /api/providers/payouts/request/
HTTP Method: POST
Description: Submits a request for a payout of earnings.
Authentication: IsAuthenticated (Provider only)
Request Body:
JSON
{
  "amount": "1000.00", // Optional, if not specified, usually max available balance
  "notes": "Withdrawal for recent services" // Optional
}


Next.js Client Example:
JavaScript
async function requestPayout(payoutData, accessToken) {
  const response = await fetch('http://localhost:8000/api/providers/payouts/request/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payoutData),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data.message || 'Failed to request payout');
  return data;
}
// Example usage:
// requestPayout({ amount: "500.00" }, providerAccessToken);


Common HTTP Status Codes
200 OK: Request successful.
201 Created: Resource created successfully.
204 No Content: Request successful, but no content is returned (e.g., for successful deletions).
400 Bad Request: The request could not be understood or was missing required parameters due to invalid data.
401 Unauthorized: Authentication is required or the provided authentication (e.g., JWT token) is invalid or expired.
403 Forbidden: The server understood the request, but the user does not have permission to access the resource or perform the action.
404 Not Found: The requested resource could not be found on the server.
429 Too Many Requests: The user has sent too many requests in a given amount of1 time (rate limiting).
500 Internal Server Error: A generic error message, given when an unexpected condition was encountered on the server.2
Error Response Format
All error responses from the API consistently follow this format:

JSON


{
  "error": "Error message description",
  "details": {
    "field_name": ["Specific field error message"],
    "non_field_errors": ["General error message related to multiple fields or the request as a whole"]
  }
}


Authentication Headers for Protected Endpoints
For any endpoint that requires authentication, you must include your JWT access token in the Authorization header with the Bearer scheme:
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
You'll obtain this access token from the login endpoints (/api/auth/login/email/ or /api/auth/login/mobile/). Remember to refresh your access token using /api/auth/token/refresh/ when it expires.
This completes the comprehensive API documentation for your Django REST Framework Home Services platform. Each endpoint is ready for integration with your Next.js frontend application.
Do you have any specific questions about integrating these APIs into your Next.js project, or would you like to explore any particular endpoint in more detail?
