# 🚀 Complete Razorpay Setup Guide - Home Services Platform

## ✅ Implementation Status: COMPLETE

Your Razorpay payment gateway integration is now fully implemented and ready for use!

## 🎯 What's Been Implemented

### 1. **Admin-Configurable Payment System**
- ✅ Toggle between Test/Live Razorpay environments
- ✅ Secure API key management (Test & Live)
- ✅ Enable/Disable payment methods (Razorpay, COD, or both)
- ✅ COD configuration (charges, minimum order values)

### 2. **Service-Level Partial Payments**
- ✅ Configure advance payment requirements per service
- ✅ Support for percentage-based or fixed amount partial payments
- ✅ Automatic calculation of partial and remaining amounts
- ✅ Customer-friendly descriptions for partial payments

### 3. **Complete API Integration**
- ✅ Payment configuration endpoint (public)
- ✅ Partial payment calculation API
- ✅ Razorpay order creation and verification
- ✅ COD workflow with provider confirmation
- ✅ Webhook support for real-time updates
- ✅ Transaction management and refund processing

## 🔧 Quick Setup Instructions

### Step 1: Start the Server
```bash
cd "c:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend"
python manage.py runserver
```

### Step 2: Access Admin Panel
1. **URL**: http://127.0.0.1:8000/admin/
2. **Login**: Use your superuser credentials
3. **Navigate**: Payments → Payment Configurations

### Step 3: Configure Razorpay Settings
1. **Add Razorpay Keys**:
   - Test Key ID: `rzp_test_XXXXXXXXXXXX`
   - Test Key Secret: `your_test_secret`
   - Live Key ID: `rzp_live_XXXXXXXXXXXX` (when ready)
   - Live Key Secret: `your_live_secret` (when ready)

2. **Set Environment**: 
   - Development: "test"
   - Production: "live"

3. **Configure Payment Methods**:
   - Enable Razorpay: ✓
   - Enable COD: ✓ (optional)
   - COD Charge: 2.5% (example)
   - COD Minimum: ₹100 (example)

### Step 4: Configure Services for Partial Payments
1. **Navigate**: Catalogue → Services
2. **Edit any service** and configure:
   ```
   Requires Partial Payment: ✓ Yes
   Payment Type: Percentage (or Fixed Amount)
   Payment Value: 20 (for 20% advance)
   Description: "Pay 20% advance to book this service"
   ```

## 🌐 API Endpoints Ready for Next.js

### Base URL: `http://127.0.0.1:8000/api/payments/`

| Endpoint | Method | Auth | Description |
|----------|--------|------|-------------|
| `configuration/` | GET | No | Get payment settings |
| `calculate-partial/` | POST | Yes | Calculate partial payments |
| `initiate/` | POST | Yes | Create payment transaction |
| `razorpay/callback/` | POST | Yes | Verify Razorpay payment |
| `cod/confirm/` | POST | Yes | Confirm COD collection |
| `status/{id}/` | GET | Yes | Get payment status |

## 💻 Next.js Integration Examples

### 1. Get Payment Configuration
```javascript
const config = await fetch('http://127.0.0.1:8000/api/payments/configuration/')
  .then(r => r.json());

console.log(config);
// Output: {
//   "success": true,
//   "configuration": {
//     "enable_razorpay": true,
//     "enable_cod": true,
//     "cod_charge_percentage": "2.50",
//     "environment": "test",
//     "razorpay_key_id": "rzp_test_XXXXXXXXXXXX"
//   }
// }
```

### 2. Calculate Partial Payment
```javascript
const calculation = await fetch('http://127.0.0.1:8000/api/payments/calculate-partial/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    items: [
      { service_id: 1, quantity: 1 },
      { service_id: 2, quantity: 2 }
    ]
  })
}).then(r => r.json());
```

### 3. Initiate Razorpay Payment
```javascript
const payment = await fetch('http://127.0.0.1:8000/api/payments/initiate/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    order_id: "1",
    amount: "500.00",
    payment_method: "razorpay",
    currency: "INR"
  })
}).then(r => r.json());

// Use payment.razorpay_key_id and payment.razorpay_order_id for Razorpay checkout
```

### 4. Handle Razorpay Checkout
```javascript
const options = {
  key: payment.razorpay_key_id,
  amount: payment.amount * 100, // Convert to paise
  currency: payment.currency,
  name: "Home Services",
  description: `Order ${payment.order_number}`,
  order_id: payment.razorpay_order_id,
  handler: async (response) => {
    // Verify payment
    const result = await fetch('http://127.0.0.1:8000/api/payments/razorpay/callback/', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        transaction_id: payment.transaction_id,
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_order_id: response.razorpay_order_id,
        razorpay_signature: response.razorpay_signature
      })
    }).then(r => r.json());
    
    if (result.success) {
      // Payment successful - redirect to success page
      window.location.href = '/payment-success';
    }
  }
};

const rzp = new Razorpay(options);
rzp.open();
```

## 📋 Testing with Postman

### Import Collection
1. **File**: `Razorpay_Payment_APIs.postman_collection.json`
2. **Environment Variables**:
   ```
   base_url: http://127.0.0.1:8000
   auth_token: YOUR_JWT_TOKEN
   ```

### Test Sequence
1. **Get Configuration**: Test public endpoint
2. **Calculate Partial**: Test with sample service IDs
3. **Initiate Payment**: Create Razorpay order
4. **Verify Payment**: Test with dummy signature

## 🧪 Verify Implementation

### Run Test Script
```bash
python test_razorpay_integration.py
```

**Expected Output**:
```
🚀 Razorpay Integration Test Suite
==================================================
✅ Payment Configuration: Working
✅ Service Partial Payment: Working
✅ API Endpoints: Available
✅ Razorpay Package: Installed
```

## 📚 Documentation Files

1. **`RAZORPAY_API_DOCUMENTATION.md`** - Complete API reference
2. **`RAZORPAY_IMPLEMENTATION_SUMMARY.md`** - Implementation overview
3. **`RAZORPAY_PRODUCTION_SETUP.md`** - Production deployment guide
4. **`Razorpay_Payment_APIs.postman_collection.json`** - Postman collection

## 🔐 Security Features

- ✅ **Signature Verification**: All payments verified server-side
- ✅ **API Key Protection**: Keys stored securely in admin
- ✅ **Cross-Database Security**: Proper foreign key handling
- ✅ **Webhook Verification**: Secure webhook processing
- ✅ **Transaction Audit**: Complete payment logging

## 🚀 Production Deployment

### When Ready for Production:
1. **Get Razorpay Live Keys**: Complete business verification
2. **Update Admin Config**: Add live keys and switch environment
3. **Configure Webhooks**: Set production webhook URL
4. **Test Thoroughly**: Test with small amounts first
5. **Monitor**: Set up payment monitoring and alerts

## 🆘 Support & Troubleshooting

### Common Issues:
1. **"Razorpay not configured"**: Add API keys in admin panel
2. **"Payment verification failed"**: Check signature calculation
3. **"COD not enabled"**: Enable COD in payment configuration

### Debug Steps:
1. Check Django logs for errors
2. Verify admin configuration
3. Test API endpoints with Postman
4. Run test script for validation

## 🎉 You're All Set!

Your Razorpay payment gateway is now fully implemented and ready for integration with your Next.js frontend. The system supports:

- ✅ **Multiple Payment Methods**: Razorpay + COD
- ✅ **Partial Payments**: Service-level advance payment configuration
- ✅ **Admin Control**: Complete payment system management
- ✅ **Production Ready**: Secure, scalable, and well-documented

**Next Steps**: 
1. Configure your Razorpay account and API keys
2. Set up services with partial payment requirements
3. Integrate the APIs with your Next.js frontend
4. Test thoroughly before going live

Happy coding! 🚀
