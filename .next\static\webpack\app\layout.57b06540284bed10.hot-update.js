"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"28abd8df809d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NzdiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI4YWJkOGRmODA5ZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: function() { return /* binding */ CartProvider; },\n/* harmony export */   useCart: function() { return /* binding */ useCart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCart = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CartProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartSummary, setCartSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Fetch cart data\n    const refreshCart = async ()=>{\n        setIsLoading(true);\n        try {\n            console.log(\"Refreshing cart data, authenticated:\", isAuthenticated);\n            // Fetch cart data (which includes GST breakdown)\n            const cartData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.getCart();\n            console.log(\"Cart data received:\", cartData);\n            setCart(cartData);\n            // Use cart data for summary as well since it contains all the summary fields plus GST breakdown\n            if (cartData && cartData.id) {\n                const summaryFromCart = {\n                    id: cartData.id,\n                    sub_total: cartData.sub_total,\n                    tax_amount: cartData.tax_amount,\n                    discount_amount: cartData.discount_amount,\n                    minimum_order_fee_applied: cartData.minimum_order_fee_applied,\n                    total_amount: cartData.total_amount,\n                    coupon_code_applied: cartData.coupon_code_applied,\n                    cgst_amount: cartData.cgst_amount,\n                    sgst_amount: cartData.sgst_amount,\n                    igst_amount: cartData.igst_amount,\n                    ugst_amount: cartData.ugst_amount,\n                    service_charge: cartData.service_charge,\n                    tax_breakdown: cartData.tax_breakdown,\n                    items_count: cartData.items_count,\n                    unique_services_count: cartData.unique_services_count,\n                    updated_at: cartData.updated_at || new Date().toISOString()\n                };\n                setCartSummary(summaryFromCart);\n            } else {\n                console.log(\"Cart data is empty or has no ID, setting cart summary to null\");\n                setCartSummary(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch cart:\", error);\n            // Handle different error scenarios\n            if (error.status === 401) {\n                console.log(\"Cart fetch failed due to authentication - user might need to login\");\n            } else if (error.status === 404) {\n                console.log(\"Cart not found - initializing empty cart\");\n            }\n            // Initialize empty cart state\n            setCart(null);\n            setCartSummary(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Initialize cart on mount and when auth state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only fetch cart data if user is authenticated\n        if (isAuthenticated) {\n            refreshCart();\n        } else {\n            // Clear cart data when user is not authenticated\n            setCart(null);\n            setCartSummary(null);\n            setIsLoading(false);\n        }\n    }, [\n        isAuthenticated\n    ]);\n    const addToCart = async function(service) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        try {\n            console.log(\"Adding to cart:\", service.id, \"quantity:\", quantity, \"authenticated:\", isAuthenticated);\n            if (!isAuthenticated) {\n                throw new Error(\"Please login to add items to cart\");\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.addToCart(service.id, quantity);\n            console.log(\"Add to cart response:\", response);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to add to cart:\", error);\n            // Provide more specific error information\n            if (error.status === 401) {\n                throw new Error(\"Please login to add items to cart\");\n            } else if (error.status === 404) {\n                throw new Error(\"Service not found\");\n            } else if (error.status === 400) {\n                throw new Error(error.message || \"Invalid request\");\n            } else {\n                throw new Error(\"Failed to add item to cart. Please try again.\");\n            }\n        }\n    };\n    const updateCartItem = async (itemId, quantity)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.updateCartItem(itemId, quantity);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to update cart item:\", error);\n            throw error;\n        }\n    };\n    const removeCartItem = async (itemId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCartItem(itemId);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove cart item:\", error);\n            throw error;\n        }\n    };\n    const clearCart = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.clearCart();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to clear cart:\", error);\n            throw error;\n        }\n    };\n    const applyCoupon = async (couponCode)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.applyCoupon(couponCode);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to apply coupon:\", error);\n            throw error;\n        }\n    };\n    const removeCoupon = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCoupon();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove coupon:\", error);\n            throw error;\n        }\n    };\n    const getTotalItems = ()=>{\n        return (cartSummary === null || cartSummary === void 0 ? void 0 : cartSummary.items_count) || 0;\n    };\n    const value = {\n        cart,\n        cartSummary,\n        isLoading,\n        addToCart,\n        updateCartItem,\n        removeCartItem,\n        clearCart,\n        applyCoupon,\n        removeCoupon,\n        refreshCart,\n        getTotalItems\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\contexts\\\\CartContext.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CartProvider, \"F9YQ2jhR0j9KfNAPU2KBxUpgfUc=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = CartProvider;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/CartContext.tsx\n"));

/***/ })

});